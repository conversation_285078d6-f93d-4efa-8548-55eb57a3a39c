"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useIsMobile } from "@/hooks/use-mobile"

interface DiscountTypeComboboxProps {
  selectedType: 'ALL' | 'CHAMPION_SKIN' | 'CHAMPION'
  onSelectionChange: (type: 'ALL' | 'CHAMPION_SKIN' | 'CHAMPION') => void
  placeholder?: string
  className?: string
}

// Helper function to get type display info
function getTypeDisplayInfo(type: 'ALL' | 'CHAMPION_SKIN' | 'CHAMPION') {
  switch (type) {
    case 'ALL':
      return {
        label: 'All Items'
      }
    case 'CHAMPION_SKIN':
      return {
        label: 'Skins'
      }
    case 'CHAMPION':
      return {
        label: 'Champions'
      }
  }
}

const availableTypes: ('ALL' | 'CHAMPION_SKIN' | 'CHAMPION')[] = ['ALL', 'CHAMPION_SKIN', 'CHAMPION']

export function DiscountTypeCombobox({
  selectedType,
  onSelectionChange,
  placeholder = "Select Type",
  className
}: DiscountTypeComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (type: 'ALL' | 'CHAMPION_SKIN' | 'CHAMPION') => {
    onSelectionChange(type)
    setOpen(false)
  }

  const getDisplayText = () => {
    return getTypeDisplayInfo(selectedType).label
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search types..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No type found.
              </CommandEmpty>
              <CommandGroup>
                {availableTypes.map((type) => {
                  const typeInfo = getTypeDisplayInfo(type)
                  return (
                    <CommandItem
                      key={type}
                      value={type}
                      onSelect={() => handleSelect(type)}
                      className="text-white hover:bg-amber-400/10 cursor-pointer"
                    >
                      <span className="flex-1">{typeInfo.label}</span>
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          selectedType === type ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
