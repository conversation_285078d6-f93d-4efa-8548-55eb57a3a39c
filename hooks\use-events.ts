import { useState, useEffect, useCallback } from 'react'

export interface EventData {
  eventId: string
  eventInfo: {
    currentTokenBalance: number
    endDate: string
    eventIcon: string
    eventId: string
    eventName: string
    [key: string]: any
  }
}

export interface EventsResponse {
  success: boolean
  message: string
  timestamp: string
  data: EventData[]
}

export function useEvents() {
  const [events, setEvents] = useState<EventData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchEvents = useCallback(async () => {
    // Skip API call in development when API is likely down
    if (process.env.NODE_ENV === 'development') {
      setLoading(false)
      setError('Events unavailable in development')
      setEvents([])
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('https://api.loldb.info/api/event-hub/events', {
        signal: AbortSignal.timeout(5000), // 5 second timeout
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('API returned non-JSON response')
      }

      const result: EventsResponse = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch events')
      }

      setEvents(result.data || [])
    } catch (err) {
      setError('Unable to load events')
      setEvents([])
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchEvents()
  }, [fetchEvents])

  const refetch = useCallback(() => fetchEvents(), [fetchEvents])

  return {
    events,
    loading,
    error,
    refetch
  }
}
