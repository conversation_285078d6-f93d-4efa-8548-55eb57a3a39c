"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ChromaRarityIconsSimple } from "@/components/ui/chroma-rarity-icons"
import { useIsMobile } from "@/hooks/use-mobile"

interface ChromaRaritiesComboboxProps {
  selectedRarities: string[]
  onSelectionChange: (rarities: string[]) => void
  availableRarities: string[]
  placeholder?: string
  className?: string
}

export function ChromaRaritiesCombobox({
  selectedRarities,
  onSelectionChange,
  availableRarities,
  placeholder = "All Rarities",
  className
}: ChromaRaritiesComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (rarityName: string) => {
    const isSelected = selectedRarities.includes(rarityName)
    
    if (isSelected) {
      // Remove rarity if already selected
      onSelectionChange(selectedRarities.filter(r => r !== rarityName))
    } else {
      // Add rarity if not selected
      onSelectionChange([...selectedRarities, rarityName])
    }
  }

  const getDisplayText = () => {
    if (selectedRarities.length === 0) {
      return placeholder
    } else if (selectedRarities.length === 1) {
      return selectedRarities[0]
    } else {
      return `${selectedRarities[0]} +${selectedRarities.length - 1}`
    }
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search rarities..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No rarity found.
              </CommandEmpty>
              <CommandGroup>
                {availableRarities.map((rarity) => (
                  <CommandItem
                    key={rarity}
                    value={rarity}
                    onSelect={() => handleSelect(rarity)}
                    className="text-white hover:bg-purple-400/10 cursor-pointer"
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      {rarity !== 'Others' && (
                        <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                          <ChromaRarityIconsSimple
                            rarity={rarity}
                            size={14}
                          />
                        </div>
                      )}
                      <span className="flex-1">{rarity}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedRarities.includes(rarity) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
