"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import { useIsMobile } from "@/hooks/use-mobile"

interface SkinTiersComboboxProps {
  selectedTiers: string[]
  onSelectionChange: (tiers: string[]) => void
  availableTiers: string[]
  placeholder?: string
  className?: string
}

export function SkinTiersCombobox({
  selectedTiers,
  onSelectionChange,
  availableTiers,
  placeholder = "All Rarities",
  className
}: SkinTiersComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (tierName: string) => {
    const isSelected = selectedTiers.includes(tierName)
    
    if (isSelected) {
      // Remove tier if already selected
      onSelectionChange(selectedTiers.filter(t => t !== tierName))
    } else {
      // Add tier if not selected
      onSelectionChange([...selectedTiers, tierName])
    }
  }

  const getDisplayText = () => {
    if (selectedTiers.length === 0) {
      return placeholder
    } else if (selectedTiers.length === 1) {
      return selectedTiers[0]
    } else {
      return `${selectedTiers[0]} +${selectedTiers.length - 1}`
    }
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search rarities..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No rarity found.
              </CommandEmpty>
              <CommandGroup>
                {availableTiers.map((tier) => (
                  <CommandItem
                    key={tier}
                    value={tier}
                    onSelect={() => handleSelect(tier)}
                    className="text-white hover:bg-purple-400/10 cursor-pointer"
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                        <SkinRarityIconsSimple
                          rarity={tier}
                          isLegacy={tier === "Legacy"}
                          isBase={false}
                          size={14}
                        />
                      </div>
                      <span className="flex-1">{tier}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedTiers.includes(tier) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
