import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { EventData } from '@/hooks/use-events'
import SharedLayout from '@/components/shared-layout'
import { EventHeader } from '@/components/event-header'
import { EventContent } from '@/components/event-content'

interface EventPageProps {
  params: {
    slug: string
  }
}

// Helper function to convert event name to URL slug (same as in EventsSubmenu)
function getEventSlug(eventName: string): string {
  return eventName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
}

// Helper function to convert event icon path to Community Dragon URL
function getEventIconUrl(eventIcon: string): string {
  const pathAfterAssets = eventIcon.split('/ASSETS/')[1]
  if (!pathAfterAssets) return ''
  
  return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${pathAfterAssets.toLowerCase()}`
}

// Fetch events data
async function fetchEvents(): Promise<EventData[]> {
  try {
    const response = await fetch('https://api.loldb.info/api/event-hub/events', {
      next: { revalidate: 300 } // Revalidate every 5 minutes
    })

    if (!response.ok) {
      return []
    }

    const result = await response.json()
    return result.success ? result.data || [] : []
  } catch (error) {
    return []
  }
}

// Find event by slug
async function findEventBySlug(slug: string): Promise<EventData | null> {
  const events = await fetchEvents()
  
  return events.find(event => {
    const eventSlug = getEventSlug(event.eventInfo.eventName || event.eventId)
    return eventSlug === slug
  }) || null
}

// Generate metadata for the page
export async function generateMetadata({ params }: EventPageProps): Promise<Metadata> {
  const event = await findEventBySlug(params.slug)
  
  if (!event) {
    return {
      title: 'Event Not Found - LoLDB',
      description: 'The requested event could not be found.'
    }
  }
  
  return {
    title: `${event.eventInfo.eventName || 'Event'} - LoLDB`,
    description: `Details for ${event.eventInfo.eventName || 'this event'} in League of Legends.`,
  }
}

// Generate static params for all current events
export async function generateStaticParams() {
  const events = await fetchEvents()
  
  return events.map(event => ({
    slug: getEventSlug(event.eventInfo.eventName || event.eventId)
  }))
}

export default async function EventPage({ params }: EventPageProps) {
  const event = await findEventBySlug(params.slug)

  if (!event) {
    notFound()
  }

  return (
    <SharedLayout>
      <EventHeader event={event}>
        {/* Event Content - Interactive Reward Track */}
        <EventContent eventId={event.eventId} />
      </EventHeader>
    </SharedLayout>
  )
}
