"use client"

import React, { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SortCombobox } from "@/components/ui/sort-combobox"
import { ChampionsCombobox } from "@/components/ui/champions-combobox"
import { ChromaRaritiesCombobox } from "@/components/ui/chroma-rarities-combobox"
import { ChromaColorsCombobox } from "@/components/ui/chroma-colors-combobox"
import { Search, X } from "lucide-react"
import { ChromaFilters } from "@/hooks/use-chroma-filters"
import { useChromas } from "@/hooks/use-chromas"
import { getActualChromaColorName } from "@/lib/utils/chroma-utils"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

interface ChromaSearchPanelProps {
  isOpen: boolean
  onClose: () => void
  filters: ChromaFilters
  onFiltersChange: (filters: Partial<ChromaFilters>) => void
  onResetFilters: () => void
  availableChromaRarities: string[]
  availableChampions: string[]
  availableChromaColors: string[]
  filteredCount: number
  totalCount: number
}

export default function ChromaSearchPanel({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onResetFilters,
  availableChromaRarities,
  availableChampions,
  availableChromaColors,
  filteredCount,
  totalCount
}: ChromaSearchPanelProps) {

  const { chromas } = useChromas()

  // Get color hex value from actual chroma data
  const getColorHex = React.useMemo(() => {
    const colorMap: Record<string, string> = {}

    // Build color mapping from actual chroma data
    chromas.forEach(chroma => {
      const actualColorName = getActualChromaColorName(chroma.name)
      if (actualColorName && chroma.colors && chroma.colors.length > 0) {
        // Use the first color from the colors array
        colorMap[actualColorName] = chroma.colors[0]
      }
    })

    return (colorName: string): string => {
      return colorMap[colorName] || '#8b5cf6' // Default to purple if not found
    }
  }, [chromas])

  // Local state for filters
  const [localSearch, setLocalSearch] = useState(filters.search)
  const [localChromaRarities, setLocalChromaRarities] = useState(filters.chromaRarities)
  const [localChampions, setLocalChampions] = useState(filters.champions)
  const [localChromaColors, setLocalChromaColors] = useState(filters.chromaColors)
  const [localSortField, setLocalSortField] = useState(filters.sortField)
  const [localSortDirection, setLocalSortDirection] = useState(filters.sortDirection)

  // Update local state when filters change
  useEffect(() => {
    setLocalSearch(filters.search)
    setLocalChromaRarities(filters.chromaRarities)
    setLocalChampions(filters.champions)
    setLocalChromaColors(filters.chromaColors)
    setLocalSortField(filters.sortField)
    setLocalSortDirection(filters.sortDirection)
  }, [filters])

  const handleApplyFilters = () => {
    onFiltersChange({
      search: localSearch,
      chromaRarities: localChromaRarities,
      champions: localChampions,
      chromaColors: localChromaColors,
      sortField: localSortField,
      sortDirection: localSortDirection
    })
    onClose()
  }

  const handleResetFilters = () => {
    setLocalSearch("")
    setLocalChromaRarities([])
    setLocalChampions([])
    setLocalChromaColors([])
    setLocalSortField('name')
    setLocalSortDirection('asc')
    onResetFilters()
    onClose()
  }

  return (
    <>
      {/* Mobile Only - Show only on small screens */}
      <div className="lg:hidden">
        {/* Backdrop */}
        <div
          className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
            isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={onClose}
        />

        {/* Search Panel */}
        <div
          className={`fixed top-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-b border-purple-700/30 z-50 transition-all duration-300 ease-out transform ${
            isOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white flex items-center">
                <Search className="h-5 w-5 mr-2 text-purple-400" />
                Search & Filters
              </h3>
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search chromas, champions, skins..."
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  className="pl-12 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-400/60 h-12 text-lg"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="space-y-4 mb-6">
              {/* Chroma Rarities Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Chroma Rarities</label>
                <ChromaRaritiesCombobox
                  selectedRarities={localChromaRarities}
                  onSelectionChange={setLocalChromaRarities}
                  availableRarities={availableChromaRarities}
                  placeholder="All Rarities"
                  className="w-full"
                />
              </div>

              {/* Champion Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Champions</label>
                <ChampionsCombobox
                  selectedChampions={localChampions}
                  onSelectionChange={setLocalChampions}
                  availableChampions={availableChampions}
                  placeholder="Select Champions"
                  className="w-full"
                />
              </div>

              {/* Chroma Colors Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Chroma Colors</label>
                <ChromaColorsCombobox
                  selectedColors={localChromaColors}
                  onSelectionChange={setLocalChromaColors}
                  availableColors={availableChromaColors}
                  placeholder="All Colors"
                  className="w-full"
                />
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
                <SortCombobox
                  selectedValue={`${localSortField}-${localSortDirection}`}
                  onSelectionChange={(value: string) => {
                    const [field, direction] = value.split('-') as [typeof localSortField, typeof localSortDirection]
                    setLocalSortField(field)
                    setLocalSortDirection(direction)
                  }}
                  options={[
                    { value: "name-asc", label: "Name (A–Z)" },
                    { value: "name-desc", label: "Name (Z–A)" },
                    { value: "release-desc", label: "Newest First" },
                    { value: "release-asc", label: "Oldest First" },
                    { value: "price-asc", label: "Price (Low to High)" },
                    { value: "price-desc", label: "Price (High to Low)" }
                  ]}
                  placeholder="Sort by..."
                  className="w-full"
                  hoverColor="purple-400/10"
                />
              </div>
            </div>

            {/* Results Count */}
            <div className="mb-6 p-3 bg-gray-800/30 rounded-lg border border-purple-700/20">
              <p className="text-sm text-gray-300">
                Showing <span className="text-purple-400 font-medium">{filteredCount}</span> of{' '}
                <span className="text-gray-100 font-medium">{totalCount}</span> chromas
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                onClick={handleResetFilters}
                variant="outline"
                className="border-gray-700/30 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-3"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-5 w-5" />
              </Button>
              <Button
                onClick={handleApplyFilters}
                className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
