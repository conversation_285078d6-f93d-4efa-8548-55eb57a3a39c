import { useState, useEffect, useCallback } from 'react'

export interface RewardOption {
  cardSize: string
  celebrationType: string
  headerType: string
  overrideFooter: string
  rewardDescription: string
  rewardFulfillmentSource: string
  rewardGroupId: string
  rewardItemId: string
  rewardItemType: string
  rewardName: string
  selected: boolean
  splashImagePath: string
  state: string
  thumbIconPath: string
}

export interface RewardTrackItem {
  progressRequired: number
  rewardOptions: RewardOption[]
  rewardTags: string[]
  state: string
  threshold: string
}

export interface RewardTrackResponse {
  success: boolean
  message: string
  timestamp: string
  data: RewardTrackItem[]
}

export function useRewardTrack(eventId: string) {
  const [rewardTrack, setRewardTrack] = useState<RewardTrackItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasRewardTrack, setHasRewardTrack] = useState(false)

  // Helper function to convert thumb icon path to Community Dragon URL
  const getRewardImageUrl = useCallback((thumbIconPath: string): string => {
    // Extract path after /ASSETS/
    const pathAfterAssets = thumbIconPath.split('/ASSETS/')[1]
    if (!pathAfterAssets) return ''
    
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${pathAfterAssets.toLowerCase()}`
  }, [])

  const fetchRewardTrack = useCallback(async () => {
    if (!eventId) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`https://api.loldb.info/api/event-hub/events/${eventId}/reward-track/items`, {
        next: { revalidate: 300 } // Revalidate every 5 minutes
      })
      
      if (!response.ok) {
        setHasRewardTrack(false)
        setRewardTrack([])
        return
      }
      
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        setHasRewardTrack(false)
        setRewardTrack([])
        return
      }
      
      const result: RewardTrackResponse = await response.json()
      
      if (!result.success) {
        setHasRewardTrack(false)
        setRewardTrack([])
        return
      }
      
      if (result.data && result.data.length > 0) {
        setHasRewardTrack(true)
        setRewardTrack(result.data)
      } else {
        setHasRewardTrack(false)
        setRewardTrack([])
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      setHasRewardTrack(false)
      setRewardTrack([])
    } finally {
      setLoading(false)
    }
  }, [eventId])

  useEffect(() => {
    fetchRewardTrack()
  }, [fetchRewardTrack])

  const refetch = useCallback(() => fetchRewardTrack(), [fetchRewardTrack])

  return {
    rewardTrack,
    loading,
    error,
    hasRewardTrack,
    refetch,
    getRewardImageUrl
  }
}
