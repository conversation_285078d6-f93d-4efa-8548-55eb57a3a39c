import { skinService } from "@/lib/services/skin-service"
import LootEligibleSkinsClient from "./client"
import type { ProcessedSkinData } from "@/lib/services/skin-service"
import type { Metadata } from 'next'

// Interface for the external loot eligible API response
interface LootEligibleApiResponse {
  success: boolean
  message: string
  timestamp: string
  data: {
    nonExclusive: {
      localizedName: string
      lootName: string
    }[]
    exclusive: {
      localizedName: string
      lootName: string
    }[]
  }
}

// Extended skin interface with loot eligibility
export interface LootEligibleSkin extends ProcessedSkinData {
  lootEligible: boolean
}

// Helper function to normalize skin names for matching
function normalizeSkinName(name: string): string {
  return name
    .toLowerCase()
    .trim()
    // Remove common variations
    .replace(/'/g, '') // Remove apostrophes (Kai'Sa -> kaisa)
    .replace(/\./g, '') // Remove periods (Dr. Mundo -> dr mundo)
    .replace(/:/g, '') // Remove colons (Project: Yasuo -> project yasuo)
    .replace(/-/g, ' ') // Replace hyphens with spaces
    .replace(/\s+/g, ' ') // Normalize multiple spaces
    .trim()
}

// Server function to fetch and process skin data
async function fetchLootEligibleSkins(): Promise<LootEligibleSkin[]> {
  try {
    // Fetch both all skins and loot eligible skins in parallel
    const [allSkins, lootEligibleResponse] = await Promise.all([
      skinService.getAllSkins(), // Get all skins from SkinService
      fetch('https://api.loldb.info/api/loot/eligible-skins', {
        next: { revalidate: 1800 } // Revalidate every 30 minutes
      })
    ])

    if (!lootEligibleResponse.ok) {
      throw new Error(`HTTP ${lootEligibleResponse.status}: Failed to fetch loot eligible skins`)
    }

    const lootEligibleData: LootEligibleApiResponse = await lootEligibleResponse.json()

    if (!lootEligibleData.success || !lootEligibleData.data) {
      throw new Error('Invalid loot eligible API response')
    }

    // Combine both nonExclusive and exclusive skins
    const allLootEligibleSkins = [
      ...(lootEligibleData.data.nonExclusive || []),
      ...(lootEligibleData.data.exclusive || [])
    ]

    // Create a Set of loot eligible skin names for fast lookup
    const lootEligibleNames = new Set(
      allLootEligibleSkins.map(skin => normalizeSkinName(skin.localizedName))
    )

    // Update all skins with correct loot eligibility based on external API
    const updatedSkins: LootEligibleSkin[] = allSkins.map(skin => {
      const normalizedSkinName = normalizeSkinName(skin.name)
      const isEligible = lootEligibleNames.has(normalizedSkinName)

      return {
        ...skin,
        lootEligible: isEligible
      }
    })

    return updatedSkins
  } catch (error) {
    console.error('Failed to fetch loot eligible skins:', error)
    throw error
  }
}

// ISR Server Component
export default async function LootEligibleSkinsPage() {
  try {
    // Fetch skin data at build time with ISR
    const allSkins = await fetchLootEligibleSkins()
    
    // Pass the data to the client component
    return <LootEligibleSkinsClient initialSkins={allSkins} />
  } catch (error) {
    console.error('Failed to fetch skins data:', error)
    
    // Return error state
    return <LootEligibleSkinsClient initialSkins={[]} error="Failed to load skins data" />
  }
}

// Enable ISR with 30 minute revalidation
export const revalidate = 1800

// Metadata for SEO
export const metadata: Metadata = {
  title: 'League of Legends Loot Eligible Skins | LoLDB',
  description: 'Search and discover which League of Legends skins can be obtained from chests, orbs, and other loot. Complete database with real-time eligibility status.',
  keywords: ['League of Legends', 'LoL', 'skins', 'loot', 'chests', 'orbs', 'hextech', 'eligible'],
  openGraph: {
    title: 'Loot Eligible Skins - League of Legends',
    description: 'Search and discover which League of Legends skins can be obtained from chests, orbs, and other loot.',
    type: 'website',
  },
}
