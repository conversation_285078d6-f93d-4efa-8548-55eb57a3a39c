"use client";

import type React from "react";

import { Badge } from "@/components/ui/badge";
import { ChevronRight, Lock, Star, TrendingUp } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo, useState } from "react";
import Breadcrumbs from "./breadcrumbs";
import ResponsiveHeader from "./navigation/responsive-header";

import {
  DiscountsIcon,
  featuredData,
  navigationData,
  SubmenuIcon,
} from "./navigation/navigation-data";
import { EventsSubmenu } from "./navigation/events-submenu";
import Footer from "./footer";

interface SharedLayoutProps {
  children: React.ReactNode;
  searchButton?: React.ReactNode;
}

export default function SharedLayout({
  children,
  searchButton,
}: SharedLayoutProps) {
  const [expandedItem, setExpandedItem] = useState<number | null>(null);
  const pathname = usePathname();

  const isHomePage = useMemo(() => pathname === "/", [pathname]);

  // Show global search on all pages except homepage
  const showGlobalSearch = pathname !== "/";

  const handleItemClick = (item: any, index: number) => {
    // If item has submenu, toggle it; otherwise navigate to URL
    if (item.submenu && !item.isComingSoon) {
      setExpandedItem(expandedItem === index ? null : index);
    } else if (!item.isComingSoon) {
      window.location.href = item.href;
    }
  };

  return (
    <div className="min-h-screen flex flex-col w-full">
      {/* Responsive Header */}
      <ResponsiveHeader
        searchButton={searchButton}
        showGlobalSearch={showGlobalSearch}
      />

      <div className="flex flex-1">
        {/* Sidebar - Hidden on mobile, fixed on desktop */}
        <aside className="w-72 bg-gray-950/50 backdrop-blur-sm border-r border-orange-800/20 fixed left-0 top-[73px] bottom-0 overflow-y-auto scrollbar-hide-pc z-40 hidden md:block">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-white mb-6 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-orange-400" />
              Quick Access
            </h2>
            <nav className="space-y-2">
              {navigationData.map((item, index) => (
                <div key={index} className="relative">
                  <div
                    className={`flex items-center justify-between p-3 rounded-lg hover:bg-gray-800/50 transition-all duration-200 group cursor-pointer ${
                      item.isComingSoon ? "opacity-60 cursor-not-allowed" : ""
                    } ${expandedItem === index ? "bg-gray-800/30" : ""}`}
                    onClick={() => handleItemClick(item, index)}
                  >
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <item.icon className="h-5 w-5 text-orange-400 group-hover:text-amber-400 transition-colors flex-shrink-0" />
                      <span className="text-gray-300 group-hover:text-white transition-colors font-medium text-sm whitespace-nowrap">
                        {item.label}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 flex-shrink-0">
                      <div className="w-14 flex justify-end">
                        {item.isLive && (
                          <Badge className="bg-red-600 text-white text-xs animate-pulse px-1.5 py-0.5 min-w-[32px] text-center">
                            Live
                          </Badge>
                        )}
                        {item.isComingSoon && (
                          <Badge className="bg-gray-600 text-gray-300 text-xs px-1.5 py-0.5 min-w-[32px] text-center">
                            Soon
                          </Badge>
                        )}
                        {!item.isLive && !item.isComingSoon && (
                          <Badge
                            variant="outline"
                            className="border-orange-600/30 text-orange-400 text-xs px-1.5 py-0.5 min-w-[32px] text-center justify-center"
                          >
                            {item.count}
                          </Badge>
                        )}
                      </div>
                      <ChevronRight
                        className={`h-4 w-4 text-gray-500 flex-shrink-0 transition-all duration-200 ${
                          item.submenu && !item.isComingSoon
                            ? expandedItem === index
                              ? "rotate-90 text-orange-400"
                              : "group-hover:text-orange-400"
                            : item.isComingSoon
                            ? "transition-colors"
                            : "group-hover:text-orange-400 group-hover:translate-x-1"
                        }`}
                      />
                    </div>
                  </div>

                  {/* Accordion Submenu */}
                  {item.submenu && (
                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        expandedItem === index
                          ? "max-h-[500px] opacity-100"
                          : "max-h-0 opacity-0"
                      }`}
                    >
                      <div className="pl-6 pr-3 py-2 space-y-1">
                        {item.label === "Events" ? (
                          <EventsSubmenu />
                        ) : (
                          item.submenu.map((subItem, subIndex) => (
                          subItem.isDisabled ? (
                            <div
                              key={subIndex}
                              className="flex items-center px-3 py-2 text-sm text-gray-600 cursor-not-allowed opacity-60"
                            >
                              {/* Conditional rendering: Custom icon, regular icon, or bullet point */}
                              {subItem.customIcon === "discounts" ? (
                                <DiscountsIcon className="mr-3 text-gray-600 flex-shrink-0" />
                              ) : subItem.icon ? (
                                <SubmenuIcon
                                  src={subItem.icon}
                                  alt={subItem.label}
                                  className="w-4 h-4 mr-3 text-gray-600 flex-shrink-0"
                                />
                              ) : (
                                <div className="w-2 h-2 bg-gray-600 rounded-full mr-3 flex-shrink-0"></div>
                              )}
                              <span className="flex-1">
                                {subItem.label}
                              </span>
                              {subItem.isLocked && (
                                <Lock className="h-3 w-3 text-gray-600 flex-shrink-0" />
                              )}
                            </div>
                          ) : (
                            <Link
                              key={subIndex}
                              href={subItem.href}
                              onClick={() => {
                                // Handle custom actions for currency conversion
                                if (subItem.customAction) {
                                  if (subItem.customAction === 'as-to-rp') {
                                    localStorage.setItem('conversionToolSelection', JSON.stringify({ from: 'as', to: 'rp', mode: 'ingame-to-ingame' }))
                                  } else if (subItem.customAction === 'me-to-rp') {
                                    localStorage.setItem('conversionToolSelection', JSON.stringify({ from: 'me', to: 'rp', mode: 'ingame-to-ingame' }))
                                  } else if (subItem.customAction === 'rp-to-usd') {
                                    localStorage.setItem('conversionToolSelection', JSON.stringify({ from: 'rp', to: 'usd', mode: 'ingame-to-real' }))
                                  }
                                  // Dispatch custom event to notify the conversion tool page
                                  window.dispatchEvent(new CustomEvent('conversionToolSelectionChanged'))
                                }
                              }}
                              className="flex items-center px-3 py-2 text-sm text-gray-400 hover:text-orange-300 hover:bg-gray-800/30 rounded-md transition-all duration-150 group"
                            >
                              {/* Conditional rendering: Custom icon, regular icon, or bullet point */}
                              {subItem.customIcon === "discounts" ? (
                                <DiscountsIcon className="mr-3 group-hover:text-orange-400 transition-colors flex-shrink-0" />
                              ) : subItem.icon ? (
                                <SubmenuIcon
                                  src={subItem.icon}
                                  alt={subItem.label}
                                  className="w-4 h-4 mr-3 group-hover:text-orange-400 transition-colors flex-shrink-0"
                                />
                              ) : (
                                <div className="w-2 h-2 bg-orange-400/60 rounded-full mr-3 group-hover:bg-orange-400 transition-colors flex-shrink-0"></div>
                              )}
                              <span className="group-hover:translate-x-1 transition-transform duration-150 flex-1">
                                {subItem.label}
                              </span>
                              {subItem.isLocked && (
                                <Lock className="h-3 w-3 text-gray-500 flex-shrink-0" />
                              )}
                            </Link>
                          )
                        )))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Featured section */}
            <div className="mt-8 pt-6 border-t border-gray-700/30">
              <h3 className="text-md font-semibold text-white mb-4 flex items-center">
                <Star className="h-4 w-4 mr-2 text-amber-400" />
                Featured
              </h3>
              <div className="space-y-3">
                {featuredData.map((featured, index) => (
                  <Link
                    key={index}
                    href={featured.href}
                    className={`block p-3 rounded-lg bg-gray-800/30 border ${featured.borderColor} hover:bg-gray-700/40 ${featured.hoverBorderColor} transition-all duration-200 cursor-pointer group`}
                  >
                    <h4
                      className={`text-sm font-medium ${
                        featured.color
                      } group-hover:text-${
                        featured.color.split("-")[1]
                      }-200 transition-colors mb-1`}
                    >
                      {featured.title}
                    </h4>
                    <p className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors">
                      {featured.description}
                    </p>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content - Responsive margins */}
        <main className="flex-1 relative ml-0 md:ml-72 min-h-0">
          {!isHomePage && (
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 pt-6">
              <Breadcrumbs />
            </div>
          )}
          <div className="w-full">{children}</div>
        </main>
      </div>

      {/* Footer - Responsive margins */}
      <Footer />
    </div>
  );
}
