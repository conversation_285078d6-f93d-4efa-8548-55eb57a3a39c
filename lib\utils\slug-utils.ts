/**
 * Utility functions for creating URL-friendly slugs
 */

/**
 * Create a URL-friendly slug from skin name
 */
export function createSkinSlug(skinName: string): string {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

/**
 * Create a URL-friendly slug from champion name
 */
export function createChampionSlug(championName: string): string {
  return championName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

/**
 * Create a URL-friendly slug from item name
 */
export function createItemSlug(itemName: string): string {
  return itemName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '')
}
