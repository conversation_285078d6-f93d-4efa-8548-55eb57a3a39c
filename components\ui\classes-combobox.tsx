"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import Image from "next/image"
import { useIsMobile } from "@/hooks/use-mobile"

interface ClassesComboboxProps {
  selectedClasses: string[]
  onSelectionChange: (classes: string[]) => void
  availableClasses: string[]
  placeholder?: string
  className?: string
}

// Helper function to get class icon URL
function getClassIconUrl(className: string): string {
  if (!className) {
    return 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png'
  }

  const classIcons: Record<string, string> = {
    // Standard capitalized names
    'Fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'Tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'Mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'Assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'Marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',

    // Lowercase variants from loldb.info API
    'fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',

    // Additional mappings for loldb.info API tags
    'carry': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',
    'ranged': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',
    'melee': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'jungler': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'recommended': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'pusher': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png'
  }

  // Return the icon URL or fallback to Fighter icon
  return classIcons[className] || classIcons['Fighter']
}

export function ClassesCombobox({
  selectedClasses,
  onSelectionChange,
  availableClasses,
  placeholder = "Select Classes",
  className
}: ClassesComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (cls: string) => {
    const isSelected = selectedClasses.includes(cls)
    if (isSelected) {
      onSelectionChange(selectedClasses.filter(c => c !== cls))
    } else {
      onSelectionChange([...selectedClasses, cls])
    }
  }

  const getDisplayText = () => {
    if (selectedClasses.length === 0) {
      return "All Classes"
    }
    if (selectedClasses.length === 1) {
      return selectedClasses[0]
    }
    return `Classes +${selectedClasses.length}`
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search classes..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No class found.
              </CommandEmpty>
              <CommandGroup>
                {availableClasses.map((cls) => (
                  <CommandItem
                    key={cls}
                    value={cls}
                    onSelect={() => handleSelect(cls)}
                    className="text-white hover:bg-orange-400/10 cursor-pointer"
                  >
                    <div className="flex items-center gap-2 flex-1">
                      <Image
                        src={getClassIconUrl(cls)}
                        alt={cls}
                        width={16}
                        height={16}
                        className="w-4 h-4 flex-shrink-0"
                        unoptimized
                      />
                      <span>{cls}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedClasses.includes(cls) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
