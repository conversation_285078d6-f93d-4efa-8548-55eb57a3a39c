'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { SlashIcon } from 'lucide-react'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

interface BreadcrumbsProps {
  className?: string
}

export default function Breadcrumbs({ className }: BreadcrumbsProps) {
  const pathname = usePathname()
  
  // Don't show breadcrumbs on homepage
  if (pathname === '/') {
    return null
  }

  // Split the pathname into segments
  let pathSegments = pathname.split('/').filter(Boolean)

  // Remove 'rarity' segment from breadcrumbs (e.g., /skins/rarity/legacy -> /skins/legacy)
  pathSegments = pathSegments.filter(segment => segment !== 'rarity')

  // Generate breadcrumb items
  const breadcrumbItems = []

  // Add homepage
  breadcrumbItems.push({
    label: 'Homepage',
    href: '/',
    isLast: false
  })
  
  // Build breadcrumbs from path segments
  let currentPath = ''
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === pathSegments.length - 1

    // Convert segment to readable label
    let label = segment
    let href = currentPath

    // Handle special cases
    switch (segment) {
      case 'champions':
        label = 'Champions'
        break
      case 'all':
        label = 'All Champions'
        break
      case 'free-rotation':
        label = 'Free Rotation'
        break
      case 'skins':
        label = 'Skins'
        // Always link to /skins for the skins breadcrumb
        href = '/skins'
        break
      case 'latest':
        label = 'Latest Skins'
        break
      case 'chromas':
        label = 'Chromas'
        break
      case 'legacy':
        label = 'Legacy'
        break
      case 'regular':
        label = 'Regular'
        break
      case 'epic':
        label = 'Epic'
        break
      case 'legendary':
        label = 'Legendary'
        break
      case 'ultimate':
        label = 'Ultimate'
        break
      case 'mythic':
        label = 'Mythic'
        break
      case 'exalted':
        label = 'Exalted'
        break
      case 'transcendent':
        label = 'Transcendent'
        break
      case 'items':
        label = 'Items'
        break
      case 'patch-notes':
        label = 'Patch Notes'
        break
      case 'blog':
        label = 'Blog'
        break
      case 'shop':
        label = 'Shop'
        break
      case 'sales':
        label = 'Sales'
        break
      case 'season':
        label = 'Season'
        break
      case 'countdown':
        label = 'Countdown'
        break
      case 'dates':
        label = 'Dates'
        break

      case 'rank-tracker':
        label = 'Rank Tracker'
        break
      case 'events':
        label = 'Events'
        break
      default:
        // For champion names and other dynamic segments, capitalize first letter
        if (segment.includes('-')) {
          label = segment.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' ')
        } else {
          label = segment.charAt(0).toUpperCase() + segment.slice(1)
        }
        break
    }

    breadcrumbItems.push({
      label,
      href,
      isLast
    })
  })

  return (
    <div className={`mb-6 ${className || ''}`}>
      <Breadcrumb>
        <BreadcrumbList className="text-sm">
          {breadcrumbItems.map((item, index) => (
            <React.Fragment key={index}>
              {index > 0 && (
                <BreadcrumbSeparator>
                  <SlashIcon className="h-4 w-4 text-gray-500" />
                </BreadcrumbSeparator>
              )}
              <BreadcrumbItem>
                {item.isLast ? (
                  <BreadcrumbPage className="text-orange-400 font-medium">
                    {item.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link
                      href={item.href}
                      className="text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {item.label}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
}
