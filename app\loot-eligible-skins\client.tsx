"use client"

import React, { useState, useMemo } from "react"
import SharedLayout from "@/components/shared-layout"
import ConversionBackground from "@/components/conversion-background"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, CheckCircle, XCircle, Info } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { createSkinSlug } from "@/lib/utils/slug-utils"
import { useSearchWithSuggestions } from "@/hooks/use-search-with-suggestions"
import { DidYouMean } from "@/components/ui/did-you-mean"
import { SkinTiersCombobox } from "@/components/ui/skin-tiers-combobox"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"
import { SelectedFiltersDisplay } from "@/components/ui/selected-filters-display"
import { Virtuoso } from 'react-virtuoso'
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import type { LootEligibleSkin } from "./page"

interface LootEligibleSkinsClientProps {
  initialSkins: LootEligibleSkin[]
  error?: string
}

export default function LootEligibleSkinsClient({ initialSkins, error }: LootEligibleSkinsClientProps) {
  const [selectedTiers, setSelectedTiers] = useState<string[]>([])

  // Use search with suggestions hook
  const search = useSearchWithSuggestions({
    items: initialSkins,
    getSearchableText: (skin) => [skin.name, skin.champion, skin.tier, ...(skin.skinLines || [])],
    getAvailableTerms: (skins) => {
      const terms = new Set<string>()
      skins.forEach(skin => {
        terms.add(skin.name)
        terms.add(skin.champion)
        terms.add(skin.tier)
        if (skin.skinLines) {
          skin.skinLines.forEach(line => terms.add(line))
        }
      })
      return Array.from(terms).filter(term => term && term.trim().length > 0)
    },
    debounceMs: 300,
    maxSuggestions: 3,
    minSimilarity: 0.4
  })

  // Get available tiers from all skins (same order as /skins page)
  const availableTiers = useMemo(() => {
    const tiers = Array.from(new Set(initialSkins.map(skin => skin.tier)))
    // Define the specific order for rarities (same as /skins page)
    const tierOrder = ['Legacy', 'Regular', 'Epic', 'Legendary', 'Mythic', 'Ultimate', 'Exalted', 'Transcendent']

    // Filter and sort tiers according to the defined order
    const orderedTiers = tierOrder.filter(tier => tier === 'Legacy' || tiers.includes(tier))

    return orderedTiers
  }, [initialSkins])

  // Get preset skins for default display (mix of eligible and not eligible)
  const presetSkins = useMemo(() => {
    // Get a mix of different rarities and eligibility status
    const eligible = initialSkins.filter(skin => skin.lootEligible).slice(0, 15)
    const notEligible = initialSkins.filter(skin => !skin.lootEligible).slice(0, 15)

    // Interleave them for a good mix
    const mixed = []
    const maxLength = Math.max(eligible.length, notEligible.length)
    for (let i = 0; i < maxLength; i++) {
      if (eligible[i]) mixed.push(eligible[i])
      if (notEligible[i]) mixed.push(notEligible[i])
    }

    return mixed.slice(0, 30) // Show 30 preset skins
  }, [initialSkins])

  // Get filtered skins from search hook and apply tier filtering
  const filteredSkins = useMemo(() => {
    // If only tier filter is selected (no search query), show all skins of that tier
    if (!search.debouncedSearchQuery.trim() && selectedTiers.length > 0) {
      return initialSkins.filter(skin => {
        return selectedTiers.some(selectedTier => {
          if (selectedTier === 'Legacy') {
            return skin.isLegacy
          } else {
            return skin.tier === selectedTier
          }
        })
      })
    }

    // If search query exists, filter search results by tier
    if (search.debouncedSearchQuery.trim()) {
      let results = search.searchResults

      // Apply tier filter if any tiers are selected
      if (selectedTiers.length > 0) {
        results = results.filter(skin => {
          return selectedTiers.some(selectedTier => {
            if (selectedTier === 'Legacy') {
              return skin.isLegacy
            } else {
              return skin.tier === selectedTier
            }
          })
        })
      }

      return results
    }

    // No search query and no tier filter - show preset skins
    return presetSkins
  }, [search.searchResults, search.debouncedSearchQuery, selectedTiers, initialSkins, presetSkins])

  // Reset all filters
  const resetFilters = () => {
    search.clearSearch()
    setSelectedTiers([])
  }

  // Get skin eligibility stats from processed data
  const skinStats = useMemo(() => {
    const eligible = initialSkins.filter(skin => skin.lootEligible === true).length
    const notEligible = initialSkins.filter(skin => skin.lootEligible === false).length
    
    return { eligible, notEligible, total: initialSkins.length }
  }, [initialSkins])

  // Create filters object for SelectedFiltersDisplay
  const filters = {
    search: search.searchQuery,
    tiers: selectedTiers,
    champions: [] as string[],
    skinLines: [] as string[],
    sortField: 'name' as const,
    sortDirection: 'asc' as const
  }

  // Remove filter functions
  const onRemoveFilter = (filterType: keyof typeof filters, value: string) => {
    if (filterType === 'tiers') {
      setSelectedTiers(prev => prev.filter(tier => tier !== value))
    }
  }

  const onClearCategory = (filterType: keyof typeof filters) => {
    if (filterType === 'tiers') {
      setSelectedTiers([])
    }
  }

  const getLootEligibilityIcon = (lootEligible: boolean) => {
    if (lootEligible) {
      return <CheckCircle className="h-5 w-5 text-green-400" />
    } else {
      return <XCircle className="h-5 w-5 text-red-400" />
    }
  }

  const getLootEligibilityText = (lootEligible: boolean) => {
    return lootEligible ? "Yes" : "No"
  }

  const getLootEligibilityColor = (lootEligible: boolean) => {
    return lootEligible ? "text-green-400" : "text-red-400"
  }

  // Hook to get responsive columns count
  const useResponsiveColumns = () => {
    const [columns, setColumns] = useState(1)

    React.useEffect(() => {
      const updateColumns = () => {
        if (window.innerWidth >= 1024) {
          setColumns(3) // lg: 3 columns
        } else if (window.innerWidth >= 768) {
          setColumns(2) // md: 2 columns
        } else {
          setColumns(1) // mobile: 1 column
        }
      }

      updateColumns()
      window.addEventListener('resize', updateColumns)
      return () => window.removeEventListener('resize', updateColumns)
    }, [])

    return columns
  }

  const columns = useResponsiveColumns()

  // Group skins into rows based on responsive columns
  const groupedSkins = useMemo(() => {
    const groups = []
    for (let i = 0; i < filteredSkins.length; i += columns) {
      groups.push(filteredSkins.slice(i, i + columns))
    }
    return groups
  }, [filteredSkins, columns])

  // Component for rendering a row of skins
  const SkinRow = ({ skins }: { skins: LootEligibleSkin[] }) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {skins.map((skin) => (
        <SkinItem key={skin.id} skin={skin} />
      ))}
    </div>
  )

  // Component for rendering individual skin items
  const SkinItem = ({ skin }: { skin: LootEligibleSkin }) => (
    <Link
      href={`/skins/${createSkinSlug(skin.name)}`}
      className="block group"
    >
      <Card className="bg-gray-900/60 border-gray-700/30 backdrop-blur-sm hover:border-purple-500/50 transition-all duration-200 group-hover:bg-gray-800/60">
        <CardContent className="p-4">
          <div className="flex items-start space-x-4">
            <div className="relative w-16 h-16 flex-shrink-0">
              <Image
                src={skin.image}
                alt={skin.name}
                fill
                className="object-cover rounded-lg"
                unoptimized
              />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-white text-sm mb-1 truncate">
                {skin.name}
              </h3>
              <p className="text-gray-400 text-xs mb-2">{skin.champion}</p>
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-xs flex items-center space-x-1">
                  <SkinRarityIconsSimple
                    rarity={skin.tier}
                    isLegacy={skin.isLegacy}
                    isBase={false}
                    size={12}
                  />
                  <span>{skin.tier}</span>
                </Badge>
                <div className="flex items-center space-x-1">
                  {getLootEligibilityIcon(skin.lootEligible)}
                  <span className={`text-xs font-medium ${getLootEligibilityColor(skin.lootEligible)}`}>
                    {getLootEligibilityText(skin.lootEligible)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )



  return (
    <ConversionBackground customImageUrl="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/characters/hwei/skins/skin11/images/hwei_splash_uncentered_11.skins_hwei_skin11.jpg">
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-20">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Loot Eligible Skins
            </h1>
            <p className="text-gray-300 text-lg">
              Search and discover which skins can be obtained from chests, orbs, and other loot
            </p>
            <Badge variant="secondary" className="mt-2 bg-purple-600/20 text-purple-300 border-purple-600/30">
              Database Search
            </Badge>
          </div>

          {/* Search Section with Stats */}
          <div className="max-w-6xl mx-auto mb-4">
            <div className="grid grid-cols-1 lg:grid-cols-[1fr_auto] gap-6 items-start">
              <Card className="bg-gray-900/60 border-purple-700/30 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-3">
                    <Search className="h-6 w-6 text-purple-400" />
                    Search & Filter Skins
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 items-end">
                    <div>
                      <Label htmlFor="search" className="text-gray-300 mb-2 block">
                        Enter skin name, champion, or skin line
                      </Label>
                      <Input
                        id="search"
                        type="text"
                        placeholder="e.g., Jinx, Arcane, Project..."
                        value={search.searchQuery}
                        onChange={(e) => search.setSearchQuery(e.target.value)}
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 w-full"
                        autoComplete="off"
                        autoCorrect="off"
                        autoCapitalize="off"
                        spellCheck="false"
                      />
                    </div>
                    <div>
                      <Label className="text-gray-300 mb-2 block">
                        Filter by Rarity
                      </Label>
                      <div className="flex items-center space-x-2">
                        <SkinTiersCombobox
                          selectedTiers={selectedTiers}
                          onSelectionChange={setSelectedTiers}
                          availableTiers={availableTiers}
                          placeholder="All Rarities"
                          className="w-full md:w-48"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={resetFilters}
                          className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                          title="Reset all filters"
                        >
                          <RemoveFiltersIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Stats Card */}
              <Card className="bg-gray-900/60 border-purple-700/30 backdrop-blur-sm w-full lg:w-64 lg:h-full">
                <CardContent className="p-6 lg:h-full lg:flex lg:flex-col lg:justify-center">
                  <div className="grid grid-cols-3 lg:grid-cols-1 gap-4 lg:space-y-4 lg:gap-0">
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center text-center lg:text-left">
                      <span className="text-gray-300 text-sm">Total Skins:</span>
                      <span className="text-white font-semibold text-lg lg:text-base">{skinStats.total}</span>
                    </div>
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center text-center lg:text-left">
                      <span className="text-gray-300 text-sm">Loot Eligible:</span>
                      <span className="text-green-400 font-semibold text-lg lg:text-base">{skinStats.eligible}</span>
                    </div>
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center text-center lg:text-left">
                      <span className="text-gray-300 text-sm">Not Eligible:</span>
                      <span className="text-red-400 font-semibold text-lg lg:text-base">{skinStats.notEligible}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Selected Filters Display */}
          <div className="max-w-6xl mx-auto mb-4">
            <SelectedFiltersDisplay
              filters={filters}
              onRemoveFilter={onRemoveFilter}
              onClearCategory={onClearCategory}
            />
          </div>

          {/* Error State */}
          {error && (
            <Card className="bg-red-900/20 border-red-700/50 max-w-md mx-auto">
              <CardContent className="p-6 text-center">
                <XCircle className="h-8 w-8 text-red-400 mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">Error Loading Data</h3>
                <p className="text-red-300">{error}</p>
              </CardContent>
            </Card>
          )}

          {/* Did You Mean? Suggestions */}
          {!error && !search.hasSearchResults && search.searchSuggestions.length > 0 && search.debouncedSearchQuery.trim() && selectedTiers.length === 0 && (
            <div className="max-w-6xl mx-auto mb-8">
              <DidYouMean
                suggestions={search.searchSuggestions}
                onSuggestionClick={search.handleSuggestionClick}
                variant="purple"
              />
            </div>
          )}

          {/* Results Display */}
          {!error && (
            <div className="max-w-6xl mx-auto">
              <div className="mb-4">
                <h2 className="text-xl font-semibold text-white">
                  {search.debouncedSearchQuery.trim()
                    ? `Search Results (${filteredSkins.length} found)`
                    : selectedTiers.length > 0
                    ? `${selectedTiers.join(', ')} Skins (${filteredSkins.length} found)`
                    : `Featured Skins (${filteredSkins.length} shown)`
                  }
                </h2>
                {search.debouncedSearchQuery.trim() && selectedTiers.length > 0 && (
                  <p className="text-sm text-gray-400 mt-1">
                    Filtered by: {selectedTiers.join(', ')}
                  </p>
                )}
                {!search.debouncedSearchQuery.trim() && selectedTiers.length > 0 && filteredSkins.length > 0 && (
                  <p className="text-sm text-gray-400 mt-1">
                    {filteredSkins.filter(s => s.lootEligible).length} eligible, {filteredSkins.filter(s => !s.lootEligible).length} not eligible
                  </p>
                )}
                {!search.debouncedSearchQuery.trim() && selectedTiers.length === 0 && (
                  <p className="text-sm text-gray-400 mt-1">
                    Mix of loot eligible and non-eligible skins • Use search or filters to explore more
                  </p>
                )}
              </div>

              {filteredSkins.length === 0 ? (
                <div className="text-center py-12">
                  <div className="relative mx-auto mb-4 w-32 h-32">
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_glow.png"
                      alt="No results glow"
                      width={128}
                      height={128}
                      className="absolute inset-0 opacity-60"
                      unoptimized
                    />
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_vfx.png"
                      alt="No results"
                      width={128}
                      height={128}
                      className="relative z-10"
                      unoptimized
                    />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-300 mb-2">No skins found</h3>
                  <p className="text-gray-400">
                    Try adjusting your filters or search terms
                  </p>
                </div>
              ) : (
                <div className="h-[600px] w-full">
                  <Virtuoso
                    data={groupedSkins}
                    totalCount={groupedSkins.length}
                    itemContent={(index, skinGroup) => (
                      <div className="px-2 py-2 mb-4">
                        <SkinRow skins={skinGroup} />
                      </div>
                    )}
                    style={{ height: '100%', width: '100%' }}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </SharedLayout>
    </ConversionBackground>
  )
}
