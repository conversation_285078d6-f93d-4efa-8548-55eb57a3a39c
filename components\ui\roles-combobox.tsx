"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import Image from "next/image"
import { useIsMobile } from "@/hooks/use-mobile"

interface RolesComboboxProps {
  selectedRoles: string[]
  onSelectionChange: (roles: string[]) => void
  availableRoles: string[]
  placeholder?: string
  className?: string
}

// Helper function to get position icon URL
function getPositionIconUrl(role: string): string {
  const roleMap: Record<string, string> = {
    'Top': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-top.png',
    'Jungle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-jungle.png',
    'Middle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-middle.png',
    'Bottom': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-bottom.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-utility.png'
  }
  return roleMap[role] || 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-fill.png'
}

export function RolesCombobox({
  selectedRoles,
  onSelectionChange,
  availableRoles,
  placeholder = "Select Roles",
  className
}: RolesComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (role: string) => {
    const isSelected = selectedRoles.includes(role)
    if (isSelected) {
      onSelectionChange(selectedRoles.filter(r => r !== role))
    } else {
      onSelectionChange([...selectedRoles, role])
    }
  }

  const getDisplayText = () => {
    if (selectedRoles.length === 0) {
      return "Any Role"
    }
    if (selectedRoles.length === 1) {
      return selectedRoles[0]
    }
    return `Roles +${selectedRoles.length}`
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search roles..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No role found.
              </CommandEmpty>
              <CommandGroup>
                {availableRoles.map((role) => (
                  <CommandItem
                    key={role}
                    value={role}
                    onSelect={() => handleSelect(role)}
                    className="text-white hover:bg-orange-400/10 cursor-pointer"
                  >
                    <div className="flex items-center gap-2 flex-1">
                      <Image
                        src={getPositionIconUrl(role)}
                        alt={role}
                        width={16}
                        height={16}
                        className="w-4 h-4 flex-shrink-0"
                        unoptimized
                      />
                      <span>{role}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedRoles.includes(role) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
