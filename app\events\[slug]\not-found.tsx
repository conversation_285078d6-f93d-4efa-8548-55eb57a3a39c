import Link from 'next/link'

export default function EventNotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
      <div className="container mx-auto px-4">
        <div className="max-w-md mx-auto bg-gray-800/50 backdrop-blur-sm rounded-lg border border-orange-800/20 p-8 text-center">
          {/* 404 Icon */}
          <div className="w-20 h-20 mx-auto mb-6 bg-red-600/20 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          
          {/* Error Message */}
          <h1 className="text-2xl font-bold text-white mb-4">Event Not Found</h1>
          <p className="text-gray-400 mb-6">
            The event you're looking for doesn't exist or may have ended and been removed from the system.
          </p>
          
          {/* Action Buttons */}
          <div className="space-y-3">
            <Link
              href="/events"
              className="block w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              View All Events
            </Link>
            <Link
              href="/"
              className="block w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Go Home
            </Link>
          </div>
          
          {/* Additional Info */}
          <div className="mt-6 pt-6 border-t border-gray-700/50">
            <p className="text-gray-500 text-sm">
              Event pages are automatically removed when events end to keep information current.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
