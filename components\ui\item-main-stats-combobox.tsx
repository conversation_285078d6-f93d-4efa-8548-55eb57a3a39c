"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useIsMobile } from "@/hooks/use-mobile"

interface ItemMainStatsComboboxProps {
  selectedMainStats: string[]
  onSelectionChange: (mainStats: string[]) => void
  availableMainStats: string[]
  placeholder?: string
  className?: string
}

export function ItemMainStatsCombobox({
  selectedMainStats,
  onSelectionChange,
  availableMainStats,
  placeholder = "Select main stats...",
  className
}: ItemMainStatsComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (statName: string) => {
    const isSelected = selectedMainStats.includes(statName)
    
    if (isSelected) {
      // Remove stat if already selected
      onSelectionChange(selectedMainStats.filter(s => s !== statName))
    } else {
      // Add stat if not selected
      onSelectionChange([...selectedMainStats, statName])
    }
  }

  const getDisplayText = () => {
    if (selectedMainStats.length === 0) {
      return placeholder
    } else if (selectedMainStats.length === 1) {
      return selectedMainStats[0]
    } else {
      return `${selectedMainStats[0]} +${selectedMainStats.length - 1}`
    }
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search main stats..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No main stat found.
              </CommandEmpty>
              <CommandGroup>
                {availableMainStats.map((stat) => (
                  <CommandItem
                    key={stat}
                    value={stat}
                    onSelect={() => handleSelect(stat)}
                    className="text-white hover:bg-blue-400/10 cursor-pointer"
                  >
                    <span className="flex-1">{stat}</span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedMainStats.includes(stat) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
