"use client";

import Link from "next/link";
import Image from "next/image";
import {
  ChampionsIcon,
  SkinsIcon,
  ItemsIcon,
  ShopIcon,
  MythicIcon,
  SeasonIcon,
  DiscountsIcon,
  CurrencyConversionIcon,
  EventsIcon,
} from "./navigation/navigation-data";

export default function Footer() {
  const footerLinks = [
    {
      title: "Champions",
      links: [
        {
          label: "All Champions",
          href: "/champions",
          icon: ChampionsIcon,
        },
        {
          label: "Free Rotation",
          href: "/champions/free-rotation",
          icon: ChampionsIcon,
        },
      ],
    },
    {
      title: "Cosmetics",
      links: [
        {
          label: "All Skins",
          href: "/skins",
          icon: SkinsIcon,
        },
        {
          label: "Chromas",
          href: "/skins/chromas",
          icon: SkinsIcon,
        },
        {
          label: "Loot Eligible Skins",
          href: "/loot-eligible-skins",
          icon: SkinsIcon,
        },
      ],
    },
    {
      title: "Items & Shop",
      links: [
        {
          label: "Items",
          href: "/items",
          icon: ItemsIcon,
        },
        {
          label: "Shop",
          href: "/shop",
          icon: ShopIcon,
        },
        {
          label: "Discounts",
          href: "/shop/discounts",
          icon: DiscountsIcon,
        },
        {
          label: "Mythic Shop",
          href: "/shop/mythic",
          icon: MythicIcon,
        },
      ],
    },
    {
      title: "Events",
      links: [
        {
          label: "All Events",
          href: "/events",
          icon: EventsIcon,
        },
      ],
    },
    {
      title: "Season & More",
      links: [
        {
          label: "Season Countdown",
          href: "/season/countdown",
          icon: SeasonIcon,
        },
        {
          label: "Conversion Tool",
          href: "/conversion-tool",
          icon: CurrencyConversionIcon,
        },
      ],
    },
    {
      title: "About",
      links: [
        {
          label: "Blog",
          href: "/blog",
          icon: ChampionsIcon, // Using a placeholder icon
        },
        {
          label: "Privacy Policy",
          href: "/privacy",
          icon: ChampionsIcon, // Using a placeholder icon
        },
        {
          label: "Terms of Service",
          href: "/terms",
          icon: ChampionsIcon, // Using a placeholder icon
        },
        {
          label: "Contact",
          href: "/contact",
          icon: ChampionsIcon, // Using a placeholder icon
        },
      ],
    },
  ];

  return (
    <footer className="border-t border-gray-700/50 bg-gray-950/80 backdrop-blur-sm py-12 ml-0 md:ml-72 mt-auto">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-7 gap-8 mb-8">
          {/* Logo and Description - First Column */}
          <div className="space-y-4 col-span-2 md:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <Image
                src="/images/LoLDB-Logo-Secondary.svg"
                alt="LoLDB Logo"
                width={40}
                height={40}
                className="w-10 h-10"
              />
              <Image
                src="/images/LoLDB-Logo-Text.svg"
                alt="LoLDB Text"
                width={80}
                height={24}
                className="h-6 w-auto"
              />
            </div>
            <p className="text-gray-400 text-sm max-w-md">
              Your ultimate League of Legends database with champions, items, skins, and more.
            </p>
          </div>

          {/* Service Links - Mobile: 2 columns, Desktop: individual columns */}
          <div className="col-span-2 md:col-span-2 lg:col-span-6 grid grid-cols-2 md:grid-cols-2 lg:grid-cols-6 gap-4 md:gap-6 lg:gap-8">
            {footerLinks.map((section, index) => (
              <div key={index} className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-4 border-b border-gray-700/30 pb-2">
                  {section.title}
                </h3>
                <ul className="space-y-3">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <Link
                        href={link.href}
                        className="text-gray-400 hover:text-orange-400 transition-colors duration-200 text-sm"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-700/30 pt-8">
          {/* Copyright */}
          <div className="text-center">
            <p className="text-gray-400 text-sm">
              © 2025 loldb.info - Your ultimate League of Legends database
            </p>
            <p className="text-xs text-gray-500 mt-2">
              Not affiliated with Riot Games. League of Legends is a trademark
              of Riot Games, Inc.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
