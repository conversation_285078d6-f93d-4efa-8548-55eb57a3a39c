"use client"

import { useEventChapters } from "@/hooks/use-event-chapters"
import { EventData } from "@/hooks/use-events"
import Image from "next/image"
import { useEffect, useState } from "react"
import EventBackground from "./event-background"

interface EventHeaderProps {
  event: EventData
  children: React.ReactNode
}

export function EventHeader({ event, children }: EventHeaderProps) {
  const { chapters, loading } = useEventChapters(event.eventId)
  const [countdown, setCountdown] = useState('')

  // Helper function to convert event icon path to Community Dragon URL
  const getEventIconUrl = (eventIcon: string): string => {
    const pathAfterAssets = eventIcon.split('/ASSETS/')[1]
    if (!pathAfterAssets) return ''
    
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${pathAfterAssets.toLowerCase()}`
  }

  // Helper function to format countdown
  const getCountdown = (endDate: Date): string => {
    const now = new Date()
    const timeRemaining = endDate.getTime() - now.getTime()
    
    if (timeRemaining <= 0) {
      return 'Event Ended'
    }
    
    const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24))
    const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60))
    
    if (days > 0) {
      return `${days}d ${hours}h remaining`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m remaining`
    } else {
      return `${minutes}m remaining`
    }
  }

  // Update countdown every minute
  useEffect(() => {
    const updateCountdown = () => {
      const endDate = new Date(event.eventInfo.endDate)
      setCountdown(getCountdown(endDate))
    }

    updateCountdown()
    const interval = setInterval(updateCountdown, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [event.eventInfo.endDate])

  const iconUrl = getEventIconUrl(event.eventInfo.eventIcon)
  const firstChapterBackground = chapters.length > 0 ? chapters[0].backgroundImage : undefined

  return (
    <EventBackground backgroundImage={firstChapterBackground}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-20">
        {/* Event Header */}
        <div className="flex items-center space-x-6 mb-8">
          {/* Event Icon */}
          {iconUrl && (
            <div className="flex-shrink-0">
              <Image
                src={iconUrl}
                alt={event.eventInfo.eventName || 'Event Icon'}
                width={80}
                height={80}
                className="w-20 h-20"
              />
            </div>
          )}
          
          {/* Event Info */}
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-white mb-4">
              {event.eventInfo.eventName || 'Event'}
            </h1>
            
            {/* Countdown with Clock Icon */}
            <div className="flex items-center space-x-3">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/clock-icon-gold2.svg"
                alt="Clock"
                width={24}
                height={24}
                className="w-6 h-6"
              />
              <span className="text-orange-400 text-lg font-medium">
                {countdown}
              </span>
            </div>
          </div>
        </div>

        {children}
      </div>
    </EventBackground>
  )
}
