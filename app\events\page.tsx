import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { EventData } from '@/hooks/use-events'
import SharedLayout from '@/components/shared-layout'
import EventsBackground from '@/components/events-background'

export const metadata: Metadata = {
  title: 'Events - LoLDB',
  description: 'Current and active League of Legends events.',
}

// Helper function to convert event name to URL slug
function getEventSlug(eventName: string): string {
  return eventName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

// Helper function to convert event icon path to Community Dragon URL
function getEventIconUrl(eventIcon: string): string {
  const pathAfterAssets = eventIcon.split('/ASSETS/')[1]
  if (!pathAfterAssets) return ''

  return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${pathAfterAssets.toLowerCase()}`
}

// Helper function to format countdown
function getCountdown(endDate: Date): string {
  const now = new Date()
  const timeRemaining = endDate.getTime() - now.getTime()

  if (timeRemaining <= 0) {
    return 'Ended'
  }

  const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24))
  const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `${days}d ${hours}h`
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}

// Fetch events data
async function fetchEvents(): Promise<EventData[]> {
  try {
    const response = await fetch('https://api.loldb.info/api/event-hub/events', {
      next: { revalidate: 300 } // Revalidate every 5 minutes
    })

    if (!response.ok) {
      return []
    }

    const result = await response.json()
    return result.success ? result.data || [] : []
  } catch (error) {
    return []
  }
}

export default async function EventsPage() {
  const events = await fetchEvents()

  return (
    <EventsBackground>
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-20">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">League of Legends Events</h1>
          <p className="text-gray-400 text-lg">
            Current and active events in League of Legends
          </p>
        </div>

        {/* Events Count */}
        <div className="mb-6">
          <p className="text-gray-400 text-lg">
            {events.length === 0 ? 'No active events' : `${events.length} active event${events.length !== 1 ? 's' : ''}`}
          </p>
        </div>

        {/* Events Grid */}
        {events.length === 0 ? (
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-orange-800/20 p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">No Active Events</h2>
            <p className="text-gray-400">
              There are currently no active events. Check back later for new events!
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6 w-full">
            {events.map((event) => {
              const iconUrl = getEventIconUrl(event.eventInfo.eventIcon)
              const eventSlug = getEventSlug(event.eventInfo.eventName || event.eventId)
              const endDate = new Date(event.eventInfo.endDate)
              const countdown = getCountdown(endDate)
              const isActive = endDate > new Date()

              return (
                <Link
                  key={event.eventId}
                  href={`/events/${eventSlug}`}
                  className="group bg-gray-800/50 backdrop-blur-sm rounded-lg border border-orange-800/20 hover:border-orange-600/40 transition-all duration-200 overflow-hidden"
                >
                  <div className="p-6 md:p-8 text-center">
                    {/* Event Icon */}
                    <div className="mb-6">
                      {iconUrl ? (
                        <Image
                          src={iconUrl}
                          alt={event.eventInfo.eventName || 'Event Icon'}
                          width={80}
                          height={80}
                          className="w-16 h-16 md:w-20 md:h-20 mx-auto"
                        />
                      ) : (
                        <div className="w-16 h-16 md:w-20 md:h-20 mx-auto bg-gray-700 rounded-lg flex items-center justify-center">
                          <svg className="w-8 h-8 md:w-10 md:h-10 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}
                    </div>

                    {/* Event Name */}
                    <h3 className="text-lg md:text-xl font-semibold text-white group-hover:text-orange-300 transition-colors mb-4">
                      {event.eventInfo.eventName || 'Event'}
                    </h3>

                    {/* Countdown */}
                    <div className="flex items-center justify-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-400' : 'bg-red-400'}`}></div>
                      <span className={`text-sm md:text-base font-medium ${
                        isActive ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {isActive ? `Ends in ${countdown}` : countdown}
                      </span>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        )}

        {/* Empty space to push footer down */}
        <div className="h-32 md:h-48"></div>
        </div>
      </SharedLayout>
    </EventsBackground>
  )
}
