"use client"

import SharedLayout from "@/components/shared-layout"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SortCombobox } from "@/components/ui/sort-combobox"
import { SelectedChromaFiltersDisplay } from "@/components/ui/selected-chroma-filters-display"
import { ChampionsCombobox } from "@/components/ui/champions-combobox"
import { ChromaRaritiesCombobox } from "@/components/ui/chroma-rarities-combobox"
import { ChromaColorsCombobox } from "@/components/ui/chroma-colors-combobox"
import { DidYouMean } from "@/components/ui/did-you-mean"
import ChromaSearchPanel from "@/components/search/chroma-search-panel"
import { ChromasIcon } from "@/components/navigation/navigation-data"
import { Search } from "lucide-react"
import { FilterIcon } from "@/components/ui/filter-icon"
import Image from "next/image"
import { useState, useEffect, useRef } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { use<PERSON><PERSON><PERSON><PERSON> } from "@/hooks/use-chromas"
import { useChromaFilters, ChromaFilters } from "@/hooks/use-chroma-filters"
import { useChromaMythicPrices } from "@/hooks/use-chroma-mythic-prices"
import LazyChromaGrid from "@/components/chroma/lazy-chroma-grid"
import { ChromasPageSkeleton } from "@/components/skeletons/chromas-skeleton"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"


export default function ChromasPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { chromas, loading, error, refetch, totalCount } = useChromas()
  const { mythicPrices } = useChromaMythicPrices(chromas)
  const {
    filteredChromas,
    filters,
    updateFilters,
    resetFilters,
    availableChampions,
    availableChromaRarities,
    availableChromaColors,
    searchSuggestions,
    hasSearchResults
  } = useChromaFilters(chromas, mythicPrices)

  const [isSearchPanelOpen, setIsSearchPanelOpen] = useState(false)
  const isInitializingRef = useRef(false)



  // Initialize filters from URL parameters on page load
  useEffect(() => {
    isInitializingRef.current = true

    const urlFilters: Partial<ChromaFilters> = {}

    // Handle search param
    const search = searchParams.get('search')
    if (search) urlFilters.search = search

    // Handle rarity param
    const rarity = searchParams.get('rarity')
    if (rarity) urlFilters.chromaRarities = [rarity]

    // Handle champion param
    const champion = searchParams.get('champion')
    if (champion) urlFilters.champions = [champion]

    // Handle chroma colors param
    const chromaColors = searchParams.get('chromaColors')
    if (chromaColors) urlFilters.chromaColors = [decodeURIComponent(chromaColors)]

    // Handle sort params
    const sortField = searchParams.get('sortField') as 'name' | 'release' | 'price' || 'name'
    const sortDirection = searchParams.get('sortDirection') as 'asc' | 'desc' || 'asc'
    urlFilters.sortField = sortField
    urlFilters.sortDirection = sortDirection

    // Apply URL filters
    updateFilters(urlFilters)

    // Mark initialization as complete after a short delay
    setTimeout(() => {
      isInitializingRef.current = false
    }, 100)
  }, [searchParams, updateFilters])

  // Get color hex value for display - only actual chroma color names
  const getColorHex = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      // Multi-word chroma colors from store API
      'Rose Quartz': '#f7cac9',
      'Rose Gold': '#e8b4a0',
      'Pearl White': '#f8f6f0',
      'Midnight Blue': '#191970',
      'Forest Green': '#228b22',
      'Royal Blue': '#4169e1',
      'Deep Purple': '#663399',
      'Bright Yellow': '#ffff00',
      'Dark Red': '#8b0000',
      'Light Blue': '#add8e6',
      'Hot Pink': '#ff69b4',
      'Lime Green': '#32cd32',
      'Sky Blue': '#87ceeb',
      'Sea Green': '#2e8b57',
      'Fire Red': '#ff2500',
      'Ice Blue': '#b0e0e6',
      'Sun Yellow': '#ffd700',
      'Moon Silver': '#c0c0c0',
      'Star Gold': '#ffd700',
      'Night Black': '#0c0c0c',

      // Single word chroma colors that appear in actual chroma names
      'Citrine': '#e4d00a',
      'Emerald': '#50c878',
      'Ruby': '#e0115f',
      'Sapphire': '#0f52ba',
      'Amethyst': '#9966cc',
      'Obsidian': '#3c3c3c',
      'Turquoise': '#40e0d0',
      'Aquamarine': '#7fffd4',
      'Peridot': '#e6e200',
      'Tanzanite': '#4b0082'
    }
    return colorMap[colorName] || '#8b5cf6' // Default to purple if not found
  }

  // Use responsive grid immediately to prevent layout jump
  const isClient = true

  // Custom reset handler that also navigates to clean URL
  const handleResetFilters = () => {
    resetFilters()
    // Navigate to clean URL without query parameters
    router.push('/skins/chromas')
  }

  // Handler for "Did You Mean?" suggestion clicks
  const handleSuggestionClick = (suggestion: string) => {
    updateFilters({ search: suggestion })
  }





  // Update URL when filters change
  useEffect(() => {
    // Don't update URL if we're still initializing
    if (isInitializingRef.current) {
      return
    }

    const params = new URLSearchParams()

    if (filters.search) params.set('search', filters.search)
    if (filters.chromaRarities.length === 1) params.set('rarity', filters.chromaRarities[0])
    if (filters.champions.length === 1) params.set('champion', filters.champions[0])
    if (filters.chromaColors.length === 1) params.set('chromaColors', encodeURIComponent(filters.chromaColors[0]))
    if (filters.sortField !== 'name') params.set('sortField', filters.sortField)
    if (filters.sortDirection !== 'asc') params.set('sortDirection', filters.sortDirection)

    const newUrl = params.toString() ? `/skins/chromas?${params.toString()}` : '/skins/chromas'
    const currentUrl = window.location.pathname + window.location.search

    // Only update URL if it's different from current
    // Use router.replace to avoid creating new history entries
    if (currentUrl !== newUrl) {
      router.replace(newUrl, { scroll: false })
    }
  }, [filters, router])



  if (loading) {
    return <ChromasPageSkeleton />
  }

  if (error) {
    return (
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 md:px-8 pb-8 no-top-padding">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-32 h-32">
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_glow.png"
                  alt="Failed to load glow"
                  width={128}
                  height={128}
                  className="absolute inset-0 opacity-60"
                  unoptimized
                />
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_vfx.png"
                  alt="Failed to load"
                  width={128}
                  height={128}
                  className="relative z-10"
                  unoptimized
                />
              </div>
              <h2 className="text-2xl font-bold text-white mb-2">Failed to load chromas</h2>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={refetch} variant="outline" className="border-gray-700 text-gray-300">
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </SharedLayout>
    )
  }

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 pb-8 no-top-padding max-w-full overflow-hidden">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <ChromasIcon className="h-8 w-8 text-purple-400" />
              <h1 className="text-4xl font-bold text-white">Chromas</h1>
            </div>
            {/* Mobile Search Button - Next to Title */}
            <div className="lg:hidden">
              <Button
                onClick={() => setIsSearchPanelOpen(true)}
                variant="outline"
                size="sm"
                className="border-purple-700/30 text-purple-400 hover:bg-purple-400/10 hover:border-purple-400/50 p-2"
              >
                <FilterIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <p className="text-gray-300 text-lg">
            Explore {totalCount || filteredChromas.length} color variants and chroma collections
          </p>
        </div>

        {/* Search and Filters - Desktop Only */}
        <div className="hidden lg:block mb-8 space-y-4">
          {/* Main Filters Row with Sort at Far Right */}
          <div className="flex flex-col lg:flex-row gap-4 items-end">
            {/* Left side filters group */}
            <div className="flex flex-col lg:flex-row gap-4 flex-1">
              {/* Search Bar - Reduced width */}
              <div className="relative lg:flex-1 lg:max-w-md">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search chromas, skins, or champions..."
                  value={filters.search}
                  onChange={(e) => updateFilters({ search: e.target.value })}
                  className="pl-12 bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-400/60"
                />
              </div>

              {/* Champion Filter */}
              <ChampionsCombobox
                selectedChampions={filters.champions}
                onSelectionChange={(champions) => updateFilters({ champions })}
                availableChampions={availableChampions}
                placeholder="Select Champions"
                className="w-full lg:w-48"
              />

              {/* Chroma Rarities Filter */}
              <ChromaRaritiesCombobox
                selectedRarities={filters.chromaRarities}
                onSelectionChange={(rarities) => updateFilters({ chromaRarities: rarities })}
                availableRarities={availableChromaRarities}
                placeholder="All Rarities"
                className="w-full lg:w-48"
              />

              {/* Chroma Colors Filter */}
              <ChromaColorsCombobox
                selectedColors={filters.chromaColors}
                onSelectionChange={(colors) => updateFilters({ chromaColors: colors })}
                availableColors={availableChromaColors}
                placeholder="All Colors"
                className="w-full lg:w-48"
              />


            </div>

            {/* Alphabetical Filter & Reset - Far Right */}
            <div className="flex-shrink-0 flex items-center gap-2">
              <SortCombobox
                selectedValue={`${filters.sortField}-${filters.sortDirection}`}
                onSelectionChange={(value: string) => {
                  const [field, direction] = value.split('-') as [typeof filters.sortField, typeof filters.sortDirection]
                  updateFilters({ sortField: field, sortDirection: direction })
                }}
                options={[
                  { value: "name-asc", label: "Name (A–Z)" },
                  { value: "name-desc", label: "Name (Z–A)" },
                  { value: "release-desc", label: "Newest First" },
                  { value: "release-asc", label: "Oldest First" },
                  { value: "price-asc", label: "Price (Low to High)" },
                  { value: "price-desc", label: "Price (High to Low)" }
                ]}
                placeholder="Sort by..."
                className="w-48"
                hoverColor="purple-400/10"
              />

              {/* Reset Filters Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetFilters}
                className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Selected Filters Display */}
          <SelectedChromaFiltersDisplay
            filters={filters}
            onRemoveFilter={(filterType, value) => {
              switch (filterType) {
                case 'search':
                  updateFilters({ search: '' })
                  break
                case 'chromaRarities':
                  updateFilters({ chromaRarities: filters.chromaRarities.filter(t => t !== value) })
                  break
                case 'champions':
                  updateFilters({ champions: filters.champions.filter(c => c !== value) })
                  break
                case 'chromaColors':
                  updateFilters({ chromaColors: filters.chromaColors.filter(c => c !== value) })
                  break
              }
            }}
            onClearCategory={(filterType) => {
              switch (filterType) {
                case 'search':
                  updateFilters({ search: '' })
                  break
                case 'chromaRarities':
                  updateFilters({ chromaRarities: [] })
                  break
                case 'champions':
                  updateFilters({ champions: [] })
                  break
                case 'chromaColors':
                  updateFilters({ chromaColors: [] })
                  break
              }
            }}
          />
        </div>



        {/* Results Count */}
        <div className="mb-8 text-gray-400">
          Showing {filteredChromas.length} of {totalCount || filteredChromas.length} chromas
        </div>

        {/* Did You Mean? Suggestions - Show when no search results but have suggestions */}
        {!hasSearchResults && searchSuggestions.length > 0 && filters.search.trim() && (
          <DidYouMean
            suggestions={searchSuggestions}
            onSuggestionClick={handleSuggestionClick}
            variant="purple"
            className="mb-8"
          />
        )}

        {/* Chromas Grid */}
        <div className="min-h-[60vh]">
          {filteredChromas.length > 0 ? (
            <LazyChromaGrid chromas={filteredChromas} isClient={isClient} />
          ) : (
            <div className="text-center py-12">
              <div className="relative mx-auto mb-4 w-32 h-32">
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_glow.png"
                  alt="No results glow"
                  width={128}
                  height={128}
                  className="absolute inset-0 opacity-60"
                  unoptimized
                />
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_vfx.png"
                  alt="No results"
                  width={128}
                  height={128}
                  className="relative z-10"
                  unoptimized
                />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No chromas found</h3>
              <p className="text-gray-400 mb-4">
                {filters.search || filters.champions.length > 0 || filters.chromaRarities.length > 0 || filters.chromaColors.length > 0
                  ? "Try adjusting your filters to see more results"
                  : "No chromas are currently available"}
              </p>
              {(filters.search || filters.champions.length > 0 || filters.chromaRarities.length > 0 || filters.chromaColors.length > 0) && (
                <Button
                  variant="outline"
                  onClick={resetFilters}
                  className="border-gray-700 text-gray-300"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Mobile Search Panel */}
        <ChromaSearchPanel
          isOpen={isSearchPanelOpen}
          onClose={() => setIsSearchPanelOpen(false)}
          filters={filters}
          onFiltersChange={updateFilters}
          onResetFilters={resetFilters}
          availableChromaRarities={availableChromaRarities}
          availableChampions={availableChampions}
          availableChromaColors={availableChromaColors}
          filteredCount={filteredChromas.length}
          totalCount={totalCount || filteredChromas.length}
        />
      </div>
    </SharedLayout>
  )
}
