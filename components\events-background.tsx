"use client"

import React, { useEffect } from 'react'

interface EventsBackgroundProps {
  children: React.ReactNode
  blur?: number
  brightness?: number
}

export default function EventsBackground({
  children,
  blur = 10,
  brightness = 0.25
}: EventsBackgroundProps) {
  const backgroundUrl = 'https://cmsassets.rgpub.io/sanity/images/dsfx7636/news_live/f98ad2b0f648d6077cb718d2f066aac2581d8950-2112x1440.png'

  useEffect(() => {
    if (backgroundUrl) {
      // Create background element with blur effect
      const backgroundElement = document.createElement('div')
      backgroundElement.id = 'events-background-image'
      backgroundElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 0;
        background-image: url('${backgroundUrl}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(${blur}px) brightness(${brightness}) saturate(1.2);
        transform: scale(1.1);
        pointer-events: none;
      `
      document.body.insertBefore(backgroundElement, document.body.firstChild)

      // Add lighter overlay
      const overlay = document.createElement('div')
      overlay.id = 'events-background-overlay'
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 2;
        background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.1), rgba(0,0,0,0.3)),
                    linear-gradient(to top, rgba(17, 24, 39, 0.7), transparent, rgba(17, 24, 39, 0.3));
        pointer-events: none;
      `
      document.body.insertBefore(overlay, document.body.firstChild)

      // Set main content z-index for proper layering
      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = 'relative'
        main.style.zIndex = '10'
      }

      // Set footer z-index
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = 'relative'
        footer.style.zIndex = '10'
      }

      // Set sidebar z-index
      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = '50'
      }
    }

    // Cleanup function
    return () => {
      const backgroundElement = document.getElementById('events-background-image')
      const overlayElement = document.getElementById('events-background-overlay')
      
      if (backgroundElement) {
        backgroundElement.remove()
      }
      if (overlayElement) {
        overlayElement.remove()
      }

      // Reset main content z-index
      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = ''
        main.style.zIndex = ''
      }

      // Reset footer z-index
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = ''
        footer.style.zIndex = ''
      }

      // Reset sidebar z-index
      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = ''
      }
    }
  }, [backgroundUrl, blur, brightness])

  return (
    <div className="relative min-h-screen z-10">
      {children}
    </div>
  )
}
