"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SortCombobox } from "@/components/ui/sort-combobox"
import { ChampionsCombobox } from "@/components/ui/champions-combobox"
import { SkinTiersCombobox } from "@/components/ui/skin-tiers-combobox"
import { SkinLinesCombobox } from "@/components/ui/skin-lines-combobox"
import { Search, X } from "lucide-react"
import { SkinFilters } from "@/hooks/use-skin-filters"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

interface SkinSearchPanelProps {
  isOpen: boolean
  onClose: () => void
  filters: SkinFilters
  onFiltersChange: (filters: Partial<SkinFilters>) => void
  onResetFilters: () => void
  availableTiers: string[]
  availableChampions: string[]
  availableSkinLines: string[]
  filteredCount: number
  totalCount: number
}

export default function SkinSearchPanel({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onResetFilters,
  availableTiers,
  availableChampions,
  availableSkinLines,
  filteredCount,
  totalCount
}: SkinSearchPanelProps) {


  // Local state for filters
  const [localSearch, setLocalSearch] = useState(filters.search)
  const [localTiers, setLocalTiers] = useState(filters.tiers)
  const [localChampions, setLocalChampions] = useState(filters.champions)
  const [localSkinLines, setLocalSkinLines] = useState(filters.skinLines)
  const [localSortField, setLocalSortField] = useState(filters.sortField)
  const [localSortDirection, setLocalSortDirection] = useState(filters.sortDirection)

  // Update local state when filters change
  useEffect(() => {
    setLocalSearch(filters.search)
    setLocalTiers(filters.tiers)
    setLocalChampions(filters.champions)
    setLocalSkinLines(filters.skinLines)
    setLocalSortField(filters.sortField)
    setLocalSortDirection(filters.sortDirection)
  }, [filters])

  const handleApplyFilters = () => {
    onFiltersChange({
      search: localSearch,
      tiers: localTiers,
      champions: localChampions,
      skinLines: localSkinLines,
      sortField: localSortField,
      sortDirection: localSortDirection
    })
    onClose()
  }

  const handleResetFilters = () => {
    setLocalSearch("")
    setLocalTiers([])
    setLocalChampions([])
    setLocalSkinLines([])
    setLocalSortField('name')
    setLocalSortDirection('asc')
    onResetFilters()
    onClose()
  }

  return (
    <>
      {/* Mobile Only - Show only on small screens */}
      <div className="lg:hidden">
        {/* Backdrop */}
        <div
          className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
            isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={onClose}
        />

        {/* Search Panel */}
        <div
          className={`fixed top-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-b border-purple-700/30 z-50 transition-all duration-300 ease-out transform ${
            isOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white flex items-center">
                <Search className="h-5 w-5 mr-2 text-purple-400" />
                Search & Filters
              </h3>
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search skins or champions..."
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  className="pl-12 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-400/60 h-12 text-lg"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="space-y-4 mb-6">
              {/* Skin Tiers Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Skin Rarities</label>
                <SkinTiersCombobox
                  selectedTiers={localTiers}
                  onSelectionChange={setLocalTiers}
                  availableTiers={availableTiers}
                  placeholder="All Rarities"
                  className="w-full"
                />
              </div>

              {/* Champion Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Champions</label>
                <ChampionsCombobox
                  selectedChampions={localChampions}
                  onSelectionChange={setLocalChampions}
                  availableChampions={availableChampions}
                  placeholder="Select Champions"
                  className="w-full"
                />
              </div>

              {/* Skin Lines Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Skin Lines</label>
                <SkinLinesCombobox
                  selectedSkinLines={localSkinLines}
                  onSelectionChange={setLocalSkinLines}
                  availableSkinLines={availableSkinLines}
                  placeholder="All Skin Lines"
                  className="w-full"
                />
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
                <SortCombobox
                  selectedValue={`${localSortField}-${localSortDirection}`}
                  onSelectionChange={(value: string) => {
                    const [field, direction] = value.split('-') as [typeof localSortField, typeof localSortDirection]
                    setLocalSortField(field)
                    setLocalSortDirection(direction)
                  }}
                  options={[
                    { value: "name-asc", label: "Name (A–Z)" },
                    { value: "name-desc", label: "Name (Z–A)" },
                    { value: "release-desc", label: "Newest First" },
                    { value: "release-asc", label: "Oldest First" },
                    { value: "price-asc", label: "Price (Low to High)" },
                    { value: "price-desc", label: "Price (High to Low)" }
                  ]}
                  placeholder="Sort by..."
                  className="w-full"
                  hoverColor="purple-400/10"
                />
              </div>
            </div>

            {/* Results Count */}
            <div className="mb-6 p-3 bg-gray-800/30 rounded-lg border border-purple-700/20">
              <p className="text-sm text-gray-300">
                Showing <span className="text-purple-400 font-medium">{filteredCount}</span> of{' '}
                <span className="text-gray-100 font-medium">{totalCount}</span> skins
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                onClick={handleResetFilters}
                variant="outline"
                className="border-gray-700/30 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-3"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-5 w-5" />
              </Button>
              <Button
                onClick={handleApplyFilters}
                className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
