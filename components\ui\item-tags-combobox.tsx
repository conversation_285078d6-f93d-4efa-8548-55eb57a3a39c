"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useIsMobile } from "@/hooks/use-mobile"

interface ItemTagsComboboxProps {
  selectedTags: string[]
  onSelectionChange: (tags: string[]) => void
  availableTags: string[]
  placeholder?: string
  className?: string
}

function formatTag(tag: string): string {
  // Add spaces before capital letters (except the first one)
  return tag.replace(/([a-z])([A-Z])/g, '$1 $2')
}

export function ItemTagsCombobox({
  selectedTags,
  onSelectionChange,
  availableTags,
  placeholder = "Select stats...",
  className
}: ItemTagsComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleSelect = (tagName: string) => {
    const isSelected = selectedTags.includes(tagName)
    
    if (isSelected) {
      // Remove tag if already selected
      onSelectionChange(selectedTags.filter(t => t !== tagName))
    } else {
      // Add tag if not selected
      onSelectionChange([...selectedTags, tagName])
    }
  }

  const getDisplayText = () => {
    if (selectedTags.length === 0) {
      return placeholder
    } else if (selectedTags.length === 1) {
      return formatTag(selectedTags[0])
    } else {
      return `${formatTag(selectedTags[0])} +${selectedTags.length - 1}`
    }
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            {!isMobile && (
              <CommandInput
                placeholder="Search stats..."
                className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
              />
            )}
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No tag found.
              </CommandEmpty>
              <CommandGroup>
                {availableTags.map((tag) => (
                  <CommandItem
                    key={tag}
                    value={tag}
                    onSelect={() => handleSelect(tag)}
                    className="text-white hover:bg-blue-400/10 cursor-pointer"
                  >
                    <span className="flex-1">{formatTag(tag)}</span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedTags.includes(tag) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
