"use client"

import { useEvents } from "@/hooks/use-events"
import Link from "next/link"
import Image from "next/image"
import { Lock } from "lucide-react"

interface EventsSubmenuProps {
  onLinkClick?: () => void
  isMobile?: boolean
}

export function EventsSubmenu({ onLinkClick, isMobile = false }: EventsSubmenuProps) {
  const { events, loading, error } = useEvents()

  // Helper function to convert event icon path to Community Dragon URL
  const getEventIconUrl = (eventIcon: string) => {
    // Extract path after /ASSETS/ and lowercase it
    const pathAfterAssets = eventIcon.split('/ASSETS/')[1]
    if (!pathAfterAssets) return ''

    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${pathAfterAssets.toLowerCase()}`
  }

  if (loading) {
    return (
      <div className="space-y-1">
        <div className="flex items-center px-3 py-2 text-sm text-gray-400">
          <div className="w-4 h-4 bg-gray-600 rounded mr-3 animate-pulse"></div>
          <span>Loading events...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-1">
        <div className="flex items-center px-3 py-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-gray-600 rounded-full mr-3"></div>
          <span>No events found</span>
        </div>
      </div>
    )
  }

  if (events.length === 0) {
    return (
      <div className="space-y-1">
        <div className="flex items-center px-3 py-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-gray-600 rounded-full mr-3"></div>
          <span>No active events</span>
        </div>
      </div>
    )
  }

  // Helper function to convert event name to URL slug
  const getEventSlug = (eventName: string) => {
    return eventName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .trim()
  }

  return (
    <div className="space-y-1">
      {events.map((event) => {
        const iconUrl = getEventIconUrl(event.eventInfo.eventIcon)
        const eventSlug = getEventSlug(event.eventInfo.eventName || event.eventId)

        return (
          <Link
            key={event.eventId}
            href={`/events/${eventSlug}`}
            onClick={onLinkClick}
            className={`flex items-center px-3 py-2 text-sm text-gray-400 hover:text-orange-300 hover:bg-gray-800/30 rounded-md transition-all duration-150 group ${
              isMobile ? 'hover:text-white hover:bg-orange-600/20' : ''
            }`}
          >
            {iconUrl ? (
              <Image
                src={iconUrl}
                alt="Event Icon"
                width={16}
                height={16}
                className="w-4 h-4 mr-3 group-hover:text-orange-400 transition-colors flex-shrink-0"
              />
            ) : (
              <div className="w-2 h-2 bg-orange-400/60 rounded-full mr-3 group-hover:bg-orange-400 transition-colors flex-shrink-0"></div>
            )}
            <span className={`group-hover:translate-x-1 transition-transform duration-150 flex-1 ${
              isMobile ? 'group-hover:translate-x-0' : ''
            }`}>
              {event.eventInfo.eventName || `Event ${event.eventId.slice(0, 8)}...`}
            </span>
          </Link>
        )
      })}
    </div>
  )
}
