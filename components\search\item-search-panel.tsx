"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SortCombobox } from "@/components/ui/sort-combobox"
import { ItemTagsCombobox } from "@/components/ui/item-tags-combobox"
import { Search, X } from "lucide-react"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

interface ItemFilters {
  search: string
  tags: string[]
  priceRange: [number, number]
  sortField: 'name' | 'price'
  sortDirection: 'asc' | 'desc'
}

interface ItemSearchPanelProps {
  isOpen: boolean
  onClose: () => void
  filters: ItemFilters
  onFiltersChange: (filters: Partial<ItemFilters>) => void
  onResetFilters: () => void
  availableTags: string[]
  filteredCount: number
  totalCount: number
}

export default function ItemSearchPanel({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onResetFilters,
  availableTags,
  filteredCount,
  totalCount
}: ItemSearchPanelProps) {


  // Local state for filters
  const [localSearch, setLocalSearch] = useState(filters.search)
  const [localTags, setLocalTags] = useState(filters.tags)
  const [localSortField, setLocalSortField] = useState(filters.sortField)
  const [localSortDirection, setLocalSortDirection] = useState(filters.sortDirection)

  // Update local state when filters change
  useEffect(() => {
    setLocalSearch(filters.search)
    setLocalTags(filters.tags)
    setLocalSortField(filters.sortField)
    setLocalSortDirection(filters.sortDirection)
  }, [filters])

  const handleApplyFilters = () => {
    onFiltersChange({
      search: localSearch,
      tags: localTags,
      sortField: localSortField,
      sortDirection: localSortDirection
    })
    onClose()
  }

  const handleResetFilters = () => {
    setLocalSearch('')
    setLocalTags([])
    setLocalSortField('name')
    setLocalSortDirection('asc')
    onResetFilters()
    onClose()
  }

  const handleClose = () => {
    // Reset local state to current filters when closing without applying
    setLocalSearch(filters.search)
    setLocalTags(filters.tags)
    setLocalSortField(filters.sortField)
    setLocalSortDirection(filters.sortDirection)
    onClose()
  }

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  return (
    <>
      {/* Mobile Only - Show only on small screens */}
      <div className="lg:hidden">
        {/* Backdrop */}
        <div
          className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
            isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={handleClose}
        />

        {/* Search Panel */}
        <div
          className={`fixed top-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-b border-blue-700/30 z-50 transition-all duration-300 ease-out transform ${
            isOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
          <div className="p-4 max-h-screen overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white flex items-center">
                <Search className="h-5 w-5 mr-2 text-blue-400" />
                Search & Filters
              </h3>
              <Button
                onClick={handleClose}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search items..."
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  className="pl-12 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-blue-400/60 h-12 text-lg"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="space-y-4 mb-6">
              {/* Tags Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                <ItemTagsCombobox
                  selectedTags={localTags}
                  onSelectionChange={setLocalTags}
                  availableTags={availableTags}
                  placeholder="Select stats..."
                  className="w-full"
                />
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
                <SortCombobox
                  selectedValue={`${localSortField}-${localSortDirection}`}
                  onSelectionChange={(value: string) => {
                    const [field, direction] = value.split('-') as [typeof localSortField, typeof localSortDirection]
                    setLocalSortField(field)
                    setLocalSortDirection(direction)
                  }}
                  options={[
                    { value: "name-asc", label: "Name (A–Z)" },
                    { value: "name-desc", label: "Name (Z–A)" },
                    { value: "price-asc", label: "Price (Low to High)" },
                    { value: "price-desc", label: "Price (High to Low)" }
                  ]}
                  placeholder="Sort by..."
                  className="w-full"
                  hoverColor="blue-400/10"
                />
              </div>
            </div>

            {/* Results Count */}
            <div className="mb-6 p-3 bg-gray-800/30 rounded-lg border border-blue-700/20">
              <p className="text-sm text-gray-300">
                Showing <span className="text-blue-400 font-medium">{filteredCount}</span> of{' '}
                <span className="text-gray-100 font-medium">{totalCount}</span> items
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                onClick={handleResetFilters}
                variant="outline"
                className="border-gray-700/30 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-3"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-5 w-5" />
              </Button>
              <Button
                onClick={handleApplyFilters}
                className="flex-1 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
