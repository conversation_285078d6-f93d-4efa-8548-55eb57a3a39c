  "use client"

  import UniversalCham<PERSON><PERSON><PERSON> from "@/components/champion/universal-champion-card"
import SearchPanel from "@/components/search/search-panel"
import SharedLayout from "@/components/shared-layout"
import { AllChampionsGridSkeleton } from "@/components/skeletons/all-champions-skeleton"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ClassesCombobox } from "@/components/ui/classes-combobox"
import { FilterIcon } from "@/components/ui/filter-icon"
import { Input } from "@/components/ui/input"
import { RolesCombobox } from "@/components/ui/roles-combobox"
import { SortCombobox } from "@/components/ui/sort-combobox"
import { SelectedChampionFiltersDisplay } from "@/components/ui/selected-champion-filters-display"
import { useChampionFilters } from "@/hooks/use-champions"
import { ChampionStats } from "@/lib/services/champion-service"
import { ChampionWithStoreData } from "@/lib/types/league-api"
import { getPositionIconUrl } from "@/lib/utils/champion-data-utils"
import { Search, TrendingUp } from "lucide-react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

  // Utility function for smooth scroll to top
  const scrollToTop = () => {
    if (typeof window !== 'undefined') {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  }

  const roleDisplayOrder = ["Top", "Jungle", "Middle", "Bottom", "Support"]
  const championClasses = ["Any", "Fighter", "Tank", "Mage", "Assassin", "Support", "Marksman"]

  export default function AllChampionsClient({ 
    initialChampions,
    initialStats
  }: {
    initialChampions: ChampionWithStoreData[];
    initialStats: ChampionStats;
  }) {
    const router = useRouter()
    const { filteredChampions, filters, updateFilters, resetFilters } = useChampionFilters(initialChampions)
    const [isSearchPanelOpen, setIsSearchPanelOpen] = useState(false)
    const [preSelectRole, setPreSelectRole] = useState<string | undefined>(undefined)

    // Use API data
    const displayChampions = filteredChampions

    // Determine if we should show "no results" - only when not loading and we have processed data
    const shouldShowNoResults = displayChampions.length === 0

    const handleResetFilters = () => {
      resetFilters()
      // Navigate to clean URL without query parameters
      router.push('/champions')
      // Don't scroll to top when resetting filters
    }

    const handleRemoveFilter = (filterType: string, value: string) => {
      if (filterType === 'roles') {
        const newRoles = filters.roles.filter((role: string) => role !== value)
        updateFilters({ roles: newRoles })
      } else if (filterType === 'classes') {
        const newClasses = filters.classes.filter((cls: string) => cls !== value)
        updateFilters({ classes: newClasses })
      }
    }

    const handleClearCategory = (filterType: string) => {
      if (filterType === 'roles') {
        updateFilters({ roles: [] })
      } else if (filterType === 'classes') {
        updateFilters({ classes: [] })
      }
    }

    const searchButton = (
      <Button
        onClick={() => setIsSearchPanelOpen(true)}
        variant="outline"
        size="sm"
        className="border-orange-700/30 text-orange-400 hover:bg-orange-400/10 hover:border-orange-400/50 p-2"
      >
        <FilterIcon className="h-4 w-4" />
      </Button>
    )

    return (
      <SharedLayout>
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-4 sm:pb-6 lg:pb-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/champions_rewards.svg"
                    alt="Champions"
                    width={32}
                    height={32}
                    className="w-8 h-8 text-orange-400"
                    style={{ filter: 'brightness(0) saturate(100%) invert(65%) sepia(80%) saturate(2000%) hue-rotate(5deg) brightness(95%) contrast(105%)' }}
                  />
                  <h1 className="text-4xl font-bold text-white">All Champions</h1>
                </div>
                {/* Mobile Search Button - Next to Title */}
                <div className="lg:hidden">
                  {searchButton}
                </div>
              </div>
              <p className="text-gray-300 text-lg">
                Complete roster of all {initialStats.total} League of Legends champions
              </p>
            </div>

          {/* Champion Statistics */}
          {initialStats.total > 0 && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-orange-400" />
                Champion Distribution
              </h2>
              {/* Desktop: Full row layout */}
              <div className="hidden md:grid md:grid-cols-5 gap-4 lg:gap-6">
                {roleDisplayOrder.map((role) => {
                  const count = initialStats.byRole[role] || 0
                  return (
                  <Card
                    key={role}
                    className={`bg-gray-900/40 border-gray-700/20 cursor-pointer transition-all duration-300 hover:border-orange-400/50 hover:-translate-y-1 hover:bg-gray-800/60 w-full ${
                      filters.roles.includes(role) ? 'border-orange-400/70 bg-orange-900/20' : ''
                    }`}
                    onClick={() => {
                      // On mobile: open search panel with role pre-selected
                      if (window.innerWidth < 1024) {
                        setPreSelectRole(role)
                        setIsSearchPanelOpen(true)
                      } else {
                        // On desktop: toggle role in multi-select and scroll to top
                        const newRoles = filters.roles.includes(role)
                          ? filters.roles.filter(r => r !== role)
                          : [...filters.roles, role]
                        updateFilters({ roles: newRoles })
                        scrollToTop()
                      }
                    }}
                  >
                    <CardContent className="p-3 md:p-4 lg:p-5 text-center h-full flex flex-col justify-center min-h-[90px] md:min-h-[100px] lg:min-h-[110px]">
                      <div className="flex items-center justify-center mb-1 md:mb-2">
                        <Image
                          src={getPositionIconUrl(role)}
                          alt={role}
                          width={24}
                          height={24}
                          className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 mr-2"
                        />
                        <span className={`text-lg md:text-xl lg:text-2xl font-bold transition-colors ${
                          filters.roles.includes(role) ? 'text-orange-300' : 'text-orange-400'
                        }`}>{count}</span>
                      </div>
                      <div className={`text-sm md:text-base lg:text-lg transition-colors ${
                        filters.roles.includes(role) ? 'text-white' : 'text-gray-400'
                      }`}>{role}</div>
                    </CardContent>
                  </Card>
                  )
                })}
              </div>

              {/* Mobile: 3-2 layout (O O O / O O) */}
              <div className="md:hidden flex flex-wrap justify-center gap-3 sm:gap-4">
                {roleDisplayOrder.map((role, index) => {
                  const count = initialStats.byRole[role] || 0
                  return (
                  <Card
                    key={role}
                    className={`bg-gray-900/40 border-gray-700/20 cursor-pointer transition-all duration-300 hover:border-orange-400/50 hover:-translate-y-1 hover:bg-gray-800/60 ${
                      filters.roles.includes(role) ? 'border-orange-400/70 bg-orange-900/20' : ''
                    } ${
                      index < 3 ? 'flex-1 min-w-[100px] max-w-[120px]' : 'flex-1 min-w-[120px] max-w-[140px]'
                    }`}
                    onClick={() => {
                      // On mobile: open search panel with role pre-selected
                      if (window.innerWidth < 1024) {
                        setPreSelectRole(role)
                        setIsSearchPanelOpen(true)
                      } else {
                        // On desktop: toggle role in multi-select and scroll to top
                        const newRoles = filters.roles.includes(role)
                          ? filters.roles.filter(r => r !== role)
                          : [...filters.roles, role]
                        updateFilters({ roles: newRoles })
                        scrollToTop()
                      }
                    }}
                  >
                    <CardContent className="p-3 sm:p-4 text-center h-full flex flex-col justify-center min-h-[80px] sm:min-h-[90px]">
                      <div className="flex items-center justify-center mb-1 sm:mb-2">
                        <Image
                          src={getPositionIconUrl(role)}
                          alt={role}
                          width={24}
                          height={24}
                          className="w-5 h-5 sm:w-6 sm:h-6 mr-1 sm:mr-2"
                        />
                        <span className={`text-base sm:text-lg font-bold transition-colors ${
                          filters.roles.includes(role) ? 'text-orange-300' : 'text-orange-400'
                        }`}>{count}</span>
                      </div>
                      <div className={`text-xs sm:text-sm transition-colors ${
                        filters.roles.includes(role) ? 'text-white' : 'text-gray-400'
                      }`}>{role}</div>
                    </CardContent>
                  </Card>
                  )
                })}
              </div>
            </div>
          )}

          {/* Desktop Search and Filters */}
          <div className="hidden lg:block mb-8 space-y-4">
            {/* Main Filters Row with Sort at Far Right */}
            <div className="flex flex-col lg:flex-row gap-4 items-end">
              {/* Left side filters group */}
              <div className="flex flex-col lg:flex-row gap-4 flex-1">
                {/* Search Bar - Reduced width */}
                <div className="relative lg:flex-1 lg:max-w-md">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search champions..."
                    value={filters.search}
                    onChange={(e) => updateFilters({ search: e.target.value })}
                    className="pl-12 bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-orange-400/60"
                  />
                </div>

                {/* Role Filter */}
                <RolesCombobox
                  selectedRoles={filters.roles}
                  onSelectionChange={(roles) => updateFilters({ roles })}
                  availableRoles={roleDisplayOrder}
                  placeholder="Select Roles"
                  className="w-full lg:w-48"
                />

                {/* Class Filter */}
                <ClassesCombobox
                  selectedClasses={filters.classes}
                  onSelectionChange={(classes) => updateFilters({ classes })}
                  availableClasses={championClasses.filter(className => className !== "Any")}
                  placeholder="Select Classes"
                  className="w-full lg:w-48"
                />
              </div>

              {/* Alphabetical Filter & Reset - Far Right */}
              <div className="flex-shrink-0 flex items-center gap-2">
                <SortCombobox
                  selectedValue={`${filters.sortField}-${filters.sortDirection}`}
                  onSelectionChange={(value: string) => {
                    const [field, direction] = value.split('-')
                    updateFilters({
                      sortField: field as 'name' | 'release' | 'price',
                      sortDirection: direction as 'asc' | 'desc'
                    })
                  }}
                  options={[
                    { value: "name-asc", label: "Name (A–Z)" },
                    { value: "name-desc", label: "Name (Z–A)" },
                    { value: "release-asc", label: "Oldest First" },
                    { value: "release-desc", label: "Newest First" },
                    { value: "price-asc", label: "Price (Low to High)" },
                    { value: "price-desc", label: "Price (High to Low)" }
                  ]}
                  placeholder="Sort by..."
                  className="w-48"
                  hoverColor="orange-400/10"
                />

                {/* Reset Filters Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetFilters}
                  className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                  title="Reset all filters"
                >
                  <RemoveFiltersIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Selected Filters Display */}
            <SelectedChampionFiltersDisplay
              filters={filters}
              onRemoveFilter={handleRemoveFilter}
              onClearCategory={handleClearCategory}
            />
          </div>

          {/* Results Count */}
          <div className="mb-8 text-gray-400">
            Showing {displayChampions.length} of {initialStats.total} champions
          </div>

          {/* Champions Grid */}
          <div className="min-h-[60vh]">
            {filteredChampions.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-3 sm:gap-4 md:gap-6 justify-items-center">
                {displayChampions.map((champion, index) => (
                  <UniversalChampionCard
                    key={`${champion.id}-${index}`}
                    champion={champion}
                    variant="loading-image"
                    borderColor="orange-700/20"
                    hoverColor="orange-400/50"
                  />
                ))}
              </div>
            ) : shouldShowNoResults ? (
              <div className="text-center py-12">
                <div className="relative mx-auto mb-4 w-32 h-32">
                  <Image
                    src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_glow.png"
                    alt="No results glow"
                    width={128}
                    height={128}
                    className="absolute inset-0 opacity-60"
                    unoptimized
                  />
                  <Image
                    src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_vfx.png"
                    alt="No results"
                    width={128}
                    height={128}
                    className="relative z-10"
                    unoptimized
                  />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No Champions Found</h3>
                <p className="text-gray-400 mb-4">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            ) : (
              // Show skeleton while store data is loading or champions are being processed
              <AllChampionsGridSkeleton />
            )}
          </div>
          </div>

        {/* Search Panel */}
        <SearchPanel
          isOpen={isSearchPanelOpen}
          onClose={() => {
            setIsSearchPanelOpen(false)
            setPreSelectRole(undefined) // Clear pre-selected role when closing
          }}
          searchTerm={filters.search}
          onSearchChange={(value) => updateFilters({ search: value })}
          selectedRoles={filters.roles}
          onRolesChange={(value) => updateFilters({ roles: value })}
          selectedClasses={filters.classes}
          onClassesChange={(value) => updateFilters({ classes: value })}
          onResetFilters={handleResetFilters}
          sortField={filters.sortField}
          sortDirection={filters.sortDirection}
          onSortChange={(field, direction) => updateFilters({ sortField: field, sortDirection: direction })}
          roles={roleDisplayOrder}
          classes={championClasses.filter(className => className !== "Any")}
          preSelectRole={preSelectRole}
          filteredCount={displayChampions.length}
          totalCount={initialStats.total}
    />
    </SharedLayout>
    )
  } 