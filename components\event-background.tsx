"use client"

import React, { useEffect } from 'react'

interface EventBackgroundProps {
  children: React.ReactNode
  backgroundImage?: string
  blur?: number
  brightness?: number
}

export default function EventBackground({
  children,
  backgroundImage,
  blur = 10,
  brightness = 0.25
}: EventBackgroundProps) {
  useEffect(() => {
    // Helper function to convert background image path to Community Dragon URL
    const getBackgroundImageUrl = (bgImage: string): string => {
      // Extract path after /images/
      const pathAfterImages = bgImage.split('/images/')[1]
      if (!pathAfterImages) return ''

      return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/seasons/images/${pathAfterImages.toLowerCase()}`
    }

    // Use event background if available, otherwise fallback to Kai'Sa background
    const backgroundUrl = backgroundImage
      ? getBackgroundImageUrl(backgroundImage)
      : 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/characters/kaisa/skins/skin70/images/kaisa_splash_uncentered_70.skins_kaisa_hol.jpg'

    // Always apply background (either event or fallback)
    if (backgroundUrl) {
        // Create background element with blur effect
        const backgroundElement = document.createElement('div')
        backgroundElement.id = 'event-background-image'
        backgroundElement.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          z-index: 0;
          background-image: url('${backgroundUrl}');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          filter: blur(${blur}px) brightness(${brightness}) saturate(1.2);
          transform: scale(1.1);
          pointer-events: none;
        `
        document.body.insertBefore(backgroundElement, document.body.firstChild)

        // Add lighter overlay
        const overlay = document.createElement('div')
        overlay.id = 'event-background-overlay'
        overlay.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          z-index: 2;
          background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.1), rgba(0,0,0,0.3)),
                      linear-gradient(to top, rgba(17, 24, 39, 0.7), transparent, rgba(17, 24, 39, 0.3));
          pointer-events: none;
        `
        document.body.insertBefore(overlay, document.body.firstChild)

        // Set main content z-index for proper layering
        const main = document.querySelector('main')
        if (main instanceof HTMLElement) {
          main.style.position = 'relative'
          main.style.zIndex = '10'
        }

        // Set footer z-index
        const footer = document.querySelector('footer')
        if (footer instanceof HTMLElement) {
          footer.style.position = 'relative'
          footer.style.zIndex = '10'
        }

        // Set sidebar z-index
        const sidebar = document.querySelector('aside')
        if (sidebar instanceof HTMLElement) {
          sidebar.style.zIndex = '50'
        }
    }

    // Cleanup function
    return () => {
      const backgroundElement = document.getElementById('event-background-image')
      const overlayElement = document.getElementById('event-background-overlay')
      
      if (backgroundElement) {
        backgroundElement.remove()
      }
      if (overlayElement) {
        overlayElement.remove()
      }

      // Reset main content z-index
      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = ''
        main.style.zIndex = ''
      }

      // Reset footer z-index
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = ''
        footer.style.zIndex = ''
      }

      // Reset sidebar z-index
      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = ''
      }
    }
  }, [backgroundImage, blur, brightness])

  return (
    <div className="relative min-h-screen z-10">
      {children}
    </div>
  )
}
