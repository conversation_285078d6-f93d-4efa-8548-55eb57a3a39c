import SharedLayout from "@/components/shared-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Home } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '404 - Something Went Wrong',
  description: '',
}

export default function NotFound() {
  return (
    <SharedLayout>
      <div className="container mx-auto px-4 h-[calc(100vh-73px)] flex items-center">
        <div className="max-w-4xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left side - Text content */}
            <div className="text-center lg:text-left order-2 lg:order-1">
              <div className="space-y-6">
                <div>
                  <h1 className="text-6xl lg:text-8xl font-bold text-white mb-4">
                    404
                  </h1>
                  <h2 className="text-2xl lg:text-3xl font-semibold text-gray-300 mb-4">
                    Uh-oh, we don't know what went wrong
                  </h2>
                  <p className="text-gray-400 text-lg leading-relaxed mb-6">
                    The page you're looking for seems to have vanished into the Rift.
                    Don't worry though - even the best summoners sometimes take a wrong turn!
                  </p>
                </div>
                
                <Link href="/">
                  <Button 
                    size="lg" 
                    className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 text-lg font-medium transition-colors"
                  >
                    <Home className="h-5 w-5 mr-2" />
                    Go Home
                  </Button>
                </Link>
              </div>
            </div>

            {/* Right side - Zed image */}
            <div className="flex justify-center lg:justify-end order-1 lg:order-2">
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                {/* Background glow */}
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/halloflegends/4536_hol_1_zed_what_was_that_glow.accessories_14_12.png"
                  alt="Glow effect"
                  width={400}
                  height={400}
                  className="absolute inset-0 w-full h-full object-contain opacity-60"
                  unoptimized
                />
                {/* Main Zed image */}
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/loadouts/summoneremotes/halloflegends/4536_hol_1_zed_what_was_that_inventory.accessories_14_12.png"
                  alt="Zed - What was that?"
                  width={400}
                  height={400}
                  className="relative z-10 w-full h-full object-contain"
                  unoptimized
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </SharedLayout>
  )
}
