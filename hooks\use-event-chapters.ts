import { useState, useEffect, useCallback } from 'react'

export interface EventChapter {
  backgroundImage: string
  backgroundVideo: string
  cardImage: string
  chapterEnd: number
  chapterNumber: number
  chapterStart: number
  foregroundImage: string
  levelFocus: number
  localizedDescription: string
  localizedTitle: string
  objectiveBannerImage: string
}

export interface EventChaptersResponse {
  success: boolean
  message?: string
  timestamp?: string
  data?: {
    chapters: EventChapter[]
  }
}

export function useEventChapters(eventId: string) {
  const [chapters, setChapters] = useState<EventChapter[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasChapters, setHasChapters] = useState(false)

  // Helper function to convert card image path to Community Dragon URL
  const getChapterImageUrl = useCallback((cardImage: string): string => {
    // Extract path after /images/
    const pathAfterImages = cardImage.split('/images/')[1]
    if (!pathAfterImages) return ''

    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/seasons/images/${pathAfterImages.toLowerCase()}`
  }, [])

  const fetchChapters = useCallback(async () => {
    if (!eventId) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`https://api.loldb.info/api/event-hub/events/${eventId}/chapters`, {
        next: { revalidate: 300 } // Revalidate every 5 minutes
      })
      
      if (!response.ok) {
        setHasChapters(false)
        setChapters([])
        return
      }
      
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        setHasChapters(false)
        setChapters([])
        return
      }
      
      const result: EventChaptersResponse = await response.json()
      
      if (!result.success) {
        setHasChapters(false)
        setChapters([])
        return
      }
      
      if (result.data?.chapters && result.data.chapters.length > 0) {
        setHasChapters(true)
        setChapters(result.data.chapters)
      } else {
        setHasChapters(false)
        setChapters([])
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      setHasChapters(false)
      setChapters([])
    } finally {
      setLoading(false)
    }
  }, [eventId])

  useEffect(() => {
    fetchChapters()
  }, [fetchChapters])

  const refetch = useCallback(() => fetchChapters(), [fetchChapters])

  return {
    chapters,
    loading,
    error,
    hasChapters,
    refetch,
    getChapterImageUrl
  }
}
