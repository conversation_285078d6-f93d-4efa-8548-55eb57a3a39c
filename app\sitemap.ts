import { MetadataRoute } from 'next'
import { leagueApi } from '@/lib/api/league-client'
import { skinService } from '@/lib/services/skin-service'
import { chromaService } from '@/lib/services/chroma-service'
import { createChromaURL } from '@/lib/utils/chroma-url-utils'
import { EventData } from '@/hooks/use-events'

const SITE_URL = 'https://loldb.info' // Update with your actual domain

// Helper function to create skin slug
function createSkinSlug(skinName: string): string {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Helper function to create event slug
function createEventSlug(eventName: string): string {
  return eventName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Fetch events data for sitemap
async function fetchEvents(): Promise<EventData[]> {
  try {
    const response = await fetch('https://api.loldb.info/api/event-hub/events')
    if (!response.ok) {
      return []
    }
    const result = await response.json()
    return result.success ? result.data || [] : []
  } catch (error) {
    return []
  }
}

export const headers = {
  'Content-Type': 'application/xml',
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: SITE_URL,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${SITE_URL}/champions`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${SITE_URL}/champions/free-rotation`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${SITE_URL}/skins`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${SITE_URL}/skins/chromas`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${SITE_URL}/loot-eligible-skins`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${SITE_URL}/events`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${SITE_URL}/items`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${SITE_URL}/shop/mythic`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.7,
    },
    {
      url: `${SITE_URL}/season`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${SITE_URL}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${SITE_URL}/shop`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.6,
    },
    {
      url: `${SITE_URL}/shop/discounts`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.6,
    },
    {
      url: `${SITE_URL}/season/countdown`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.5,
    },
    {
      url: `${SITE_URL}/conversion-tool`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${SITE_URL}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${SITE_URL}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ]

  try {
    // Dynamic champion pages
    const champions = await leagueApi.getChampions()
    const championPages: MetadataRoute.Sitemap = Object.keys(champions).map((championId) => ({
      url: `${SITE_URL}/champions/${championId}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }))

    // Dynamic skin pages
    let skinPages: MetadataRoute.Sitemap = []
    try {
      const allSkins = await skinService.getAllSkins()
      skinPages = allSkins.map((skin) => ({
        url: `${SITE_URL}/skins/${createSkinSlug(skin.name)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      }))
    } catch (error) {
      console.warn('Failed to generate skin pages for sitemap:', error)
    }

    // Dynamic chroma pages
    let chromaPages: MetadataRoute.Sitemap = []
    try {
      const allChromas = await chromaService.getAllChromas()
      chromaPages = allChromas.map((chroma) => ({
        url: `${SITE_URL}/skins/chromas/${createChromaURL(chroma)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.6,
      }))
    } catch (error) {
      console.warn('Failed to generate chroma pages for sitemap:', error)
    }

    // Dynamic event pages
    let eventPages: MetadataRoute.Sitemap = []
    try {
      const allEvents = await fetchEvents()
      eventPages = allEvents.map((event) => ({
        url: `${SITE_URL}/events/${createEventSlug(event.eventName)}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.8,
      }))
    } catch (error) {
      console.warn('Failed to generate event pages for sitemap:', error)
    }

    return [...staticPages, ...championPages, ...skinPages, ...chromaPages, ...eventPages]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    // Return static pages only if dynamic generation fails
    return staticPages
  }
}

// Optional: Create separate sitemaps for different content types
// This is useful if you have a lot of pages

export async function generateChampionSitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    const champions = await leagueApi.getChampions()
    return Object.keys(champions).map((championId) => ({
      url: `${SITE_URL}/champions/${championId}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }))
  } catch (error) {
    console.error('Error generating champion sitemap:', error)
    return []
  }
}

// If you want to create a sitemap index (for large sites)
// Create app/sitemap-index.ts:
/*
import { MetadataRoute } from 'next'

export default function sitemapIndex(): MetadataRoute.Sitemap {
  return [
    {
      url: 'https://loldb.info/sitemap.xml',
      lastModified: new Date(),
    },
    {
      url: 'https://loldb.info/champions-sitemap.xml',
      lastModified: new Date(),
    },
    {
      url: 'https://loldb.info/skins-sitemap.xml',
      lastModified: new Date(),
    },
  ]
}
*/
