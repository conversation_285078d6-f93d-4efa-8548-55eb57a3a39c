"use client"

import { useRewardTrack } from "@/hooks/use-reward-track"
import Image from "next/image"
import { useState, useEffect, useRef } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface InteractiveRewardTrackProps {
  eventId: string
}

export function InteractiveRewardTrack({ eventId }: InteractiveRewardTrackProps) {
  const { rewardTrack, loading, error, hasRewardTrack, getRewardImageUrl } = useRewardTrack(eventId)
  const [selectedReward, setSelectedReward] = useState<{
    name: string
    imageUrl: string
    progress: number
    isPremium: boolean
  } | null>(null)
  const [currentPage, setCurrentPage] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(2)
  const [isAnimating, setIsAnimating] = useState(false)
  const [animationDirection, setAnimationDirection] = useState<'left' | 'right'>('right')
  const progressBarRef = useRef<HTMLDivElement>(null)
  const itemsContainerRef = useRef<HTMLDivElement>(null)
  const [containerWidth, setContainerWidth] = useState(0)

  // FOOLPROOF RESPONSIVE SYSTEM - MEASURES ACTUAL SPACE AND FORCES FIT
  useEffect(() => {
    const measureAndUpdate = () => {
      if (!progressBarRef.current || !itemsContainerRef.current) return

      // Get the actual container that holds the progress bar
      const parentContainer = progressBarRef.current.parentElement
      if (!parentContainer) return

      // Measure the ACTUAL available width from the parent
      const parentWidth = parentContainer.offsetWidth

      // Account for any padding/margins on the parent
      const computedStyle = window.getComputedStyle(parentContainer)
      const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0
      const paddingRight = parseFloat(computedStyle.paddingRight) || 0
      const totalParentPadding = paddingLeft + paddingRight

      // The actual usable width
      const usableWidth = parentWidth - totalParentPadding - 32 // 32px extra safety

      // Fixed space for modern arrows and progress bar padding
      const arrowWidth = 50 // Each modern arrow button (larger)
      const arrowSpacing = 24 // Space around arrows (increased for modern design)
      const progressBarPadding = 80 // Internal padding of modern progress bar (increased)
      const extraPadding = 40 // Extra padding for hover effects
      const fixedSpace = (arrowWidth * 2) + (arrowSpacing * 4) + progressBarPadding + extraPadding

      // Available space for items
      const availableForItems = Math.max(200, usableWidth - fixedSpace)

      // Item dimensions (larger for better visual presence)
      const itemSize = 96 // Larger fixed size for all items
      const itemSpacing = 12 // More spacing for better appearance
      const totalItemWidth = itemSize + itemSpacing

      // Calculate how many can ACTUALLY fit
      const maxFit = Math.floor(availableForItems / totalItemWidth)

      // Apply your rules with ABSOLUTE guarantee they fit
      let finalCount
      if (maxFit >= 10) {
        finalCount = 10
      } else if (maxFit >= 5) {
        finalCount = 5
      } else {
        finalCount = 2
      }

      // FINAL SAFETY: Double-check the math
      const totalNeeded = (finalCount * totalItemWidth) + fixedSpace
      if (totalNeeded > usableWidth) {
        // Force down to next level
        if (finalCount === 10) finalCount = 5
        else if (finalCount === 5) finalCount = 2
        console.warn(`Forced downgrade: needed ${totalNeeded}px, have ${usableWidth}px`)
      }

      setContainerWidth(usableWidth)
      setItemsPerPage(finalCount)
    }

    // Comprehensive update system
    const scheduleUpdate = () => {
      requestAnimationFrame(() => {
        setTimeout(measureAndUpdate, 10)
      })
    }

    // Multiple attempts to catch all layout changes
    scheduleUpdate()
    setTimeout(scheduleUpdate, 50)
    setTimeout(scheduleUpdate, 200)
    setTimeout(scheduleUpdate, 500)

    // Event listeners
    window.addEventListener('resize', scheduleUpdate)

    // ResizeObserver for container changes
    let resizeObserver: ResizeObserver | null = null
    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(scheduleUpdate)
      if (progressBarRef.current) resizeObserver.observe(progressBarRef.current)
      if (progressBarRef.current?.parentElement) {
        resizeObserver.observe(progressBarRef.current.parentElement)
      }
    }

    return () => {
      window.removeEventListener('resize', scheduleUpdate)
      resizeObserver?.disconnect()
    }
  }, [hasRewardTrack, rewardTrack.length])

  // Auto-select the first reward when reward track loads
  useEffect(() => {
    if (!loading && hasRewardTrack && rewardTrack.length > 0 && !selectedReward) {
      const firstItem = rewardTrack[0]
      const firstReward = firstItem.rewardOptions[0]
      if (firstReward) {
        const imageUrl = getRewardImageUrl(firstReward.thumbIconPath)
        if (imageUrl) {
          setSelectedReward({
            name: firstReward.rewardName,
            imageUrl,
            progress: firstItem.progressRequired,
            isPremium: firstReward.headerType === 'PREMIUM'
          })
        }
      }
    }
  }, [loading, hasRewardTrack, rewardTrack, selectedReward, getRewardImageUrl])

  // Calculate pagination
  const totalPages = Math.ceil(rewardTrack.length / itemsPerPage)
  const startIndex = currentPage * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentItems = rewardTrack.slice(startIndex, endIndex)

  const goToNextPage = () => {
    if (currentPage < totalPages - 1 && !isAnimating) {
      setAnimationDirection('right')
      setIsAnimating(true)
      setCurrentPage(currentPage + 1)
      setTimeout(() => setIsAnimating(false), 300) // Match transition duration
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 0 && !isAnimating) {
      setAnimationDirection('left')
      setIsAnimating(true)
      setCurrentPage(currentPage - 1)
      setTimeout(() => setIsAnimating(false), 300) // Match transition duration
    }
  }

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-white text-xl">Loading reward track...</div>
      </div>
    )
  }

  if (error || !hasRewardTrack || rewardTrack.length === 0) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-white text-xl mb-4">No reward track available</div>
          <div className="text-gray-400">This event doesn't have an interactive reward track.</div>
        </div>
      </div>
    )
  }

  const handleRewardClick = (reward: any, progress: number) => {
    const imageUrl = getRewardImageUrl(reward.thumbIconPath)
    if (imageUrl) {
      setSelectedReward({
        name: reward.rewardName,
        imageUrl,
        progress,
        isPremium: reward.headerType === 'PREMIUM'
      })
    }
  }



  return (
    <div className="relative">
      <style jsx>{`
        @keyframes slideInFromRight {
          0% { opacity: 0; transform: translateX(20px); }
          100% { opacity: 1; transform: translateX(0); }
        }
        @keyframes slideInFromLeft {
          0% { opacity: 0; transform: translateX(-20px); }
          100% { opacity: 1; transform: translateX(0); }
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%); opacity: 0; }
          100% { transform: translateX(100%); opacity: 1; }
        }
      `}</style>
      {/* Main Content Area */}
      <div className="flex justify-center pt-16 pb-6">
        {selectedReward ? (
          <div className="text-center">
            <Image
              src={selectedReward.imageUrl}
              alt={selectedReward.name}
              width={400}
              height={400}
              className="w-48 h-48 md:w-64 md:h-64 mx-auto mb-3"
            />
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
              {selectedReward.name}
            </h2>
            <p className="text-orange-400 text-lg">
              {selectedReward.progress} EXP Required
            </p>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-white text-xl mb-4">Select a reward to view details</div>
            <div className="text-gray-400">Click on any reward in the progress bar below</div>
          </div>
        )}
      </div>

      {/* Modern Progress Bar */}
      <div className="flex justify-center px-4 overflow-hidden">
        <div
          ref={progressBarRef}
          className="relative py-4 px-6 sm:py-5 sm:px-8 lg:py-6 lg:px-10 inline-flex items-center max-w-full"
          style={{
            maxWidth: 'calc(100vw - 64px)', // More conservative max width
            gap: `${12}px`, // Use calculated gap instead of space-x classes
            background: 'linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(249, 115, 22, 0.15) 50%, rgba(234, 88, 12, 0.1) 100%)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(251, 146, 60, 0.2)',
            borderRadius: '24px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
            overflow: 'visible' // Allow hover effects to extend outside
          }}
        >
          {/* Animated background glow - diagonal movement */}
          <div
            className="absolute inset-0 overflow-hidden"
            style={{ borderRadius: '24px' }}
          >
            <div
              className="w-full h-full"
              style={{
                background: 'linear-gradient(45deg, transparent 30%, rgba(251, 146, 60, 0.15) 50%, transparent 70%)',
                animation: 'shimmer 2s ease-out 1 forwards'
              }}
            />
          </div>


          {/* Left Arrow - Modern Design */}
          <button
            onClick={goToPrevPage}
            disabled={currentPage === 0}
            className={`relative z-10 p-3 rounded-2xl transition-all duration-300 transform ${
              currentPage === 0
                ? 'text-gray-500 cursor-not-allowed opacity-50'
                : 'text-orange-300 hover:text-white hover:scale-110 hover:-translate-y-1'
            }`}
            style={{
              background: currentPage === 0
                ? 'rgba(75, 85, 99, 0.3)'
                : 'linear-gradient(135deg, rgba(251, 146, 60, 0.2) 0%, rgba(249, 115, 22, 0.3) 100%)',
              backdropFilter: 'blur(10px)',
              border: currentPage === 0
                ? '1px solid rgba(75, 85, 99, 0.3)'
                : '1px solid rgba(251, 146, 60, 0.4)',
              boxShadow: currentPage === 0
                ? 'none'
                : '0 4px 20px rgba(251, 146, 60, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
          >
            <ChevronLeft className="w-5 h-5" />
          </button>

          {/* Reward Items */}
          <div
            ref={itemsContainerRef}
            className="flex-shrink min-w-0"
            style={{ overflow: 'visible' }} // Allow hover effects to extend outside container
          >
            <div
              className="flex items-end justify-center transition-all duration-300 ease-in-out"
              key={currentPage}
              style={{ gap: '12px' }}
            >
              {currentItems.map((item, index) => {
                const reward = item.rewardOptions[0] // Take first reward option
                if (!reward) return null

                const imageUrl = getRewardImageUrl(reward.thumbIconPath)
                const tierNumber = startIndex + index + 1 // Global tier number

                return (
                  <div
                    key={startIndex + index}
                    onClick={() => handleRewardClick(reward, item.progressRequired)}
                    className="cursor-pointer group text-center transform transition-all duration-500 ease-out flex-shrink-0 relative z-10"
                    style={{
                      animation: isAnimating
                        ? `${animationDirection === 'right' ? 'slideInFromRight' : 'slideInFromLeft'} 0.3s ease-in-out`
                        : 'none',
                      width: '96px'
                    }}
                  >
                    {/* Modern Card Container */}
                    <div
                      className="relative p-3 rounded-2xl transition-all duration-500 group-hover:scale-110 group-hover:-translate-y-2"
                      style={{
                        background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%)',
                        backdropFilter: 'blur(15px)',
                        border: '1px solid rgba(148, 163, 184, 0.2)',
                        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                      }}
                    >
                      {/* Hover glow effect */}
                      <div
                        className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                        style={{
                          background: 'linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(249, 115, 22, 0.2) 100%)',
                          boxShadow: '0 0 30px rgba(251, 146, 60, 0.3)'
                        }}
                      />

                      {/* Reward Image */}
                      <div
                        className="relative z-10 rounded-xl overflow-hidden"
                        style={{ width: '72px', height: '72px' }}
                      >
                      {imageUrl ? (
                        <Image
                          src={imageUrl}
                          alt={reward.rewardName}
                          width={96}
                          height={96}
                          className="w-full h-full object-contain"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="w-8 h-8 bg-gray-600 rounded"></div>
                        </div>
                      )}
                      </div>

                      {/* Premium Lock Icon - at bottom center of card */}
                      {reward.headerType === 'PREMIUM' && (
                        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-20">
                          <div
                            className="w-4 h-4 filter drop-shadow-lg"
                            style={{
                              backgroundColor: '#cdbe91',
                              mask: 'url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/tomacco/lock.svg) no-repeat center',
                              maskSize: 'contain',
                              WebkitMask: 'url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/tomacco/lock.svg) no-repeat center',
                              WebkitMaskSize: 'contain'
                            }}
                          />
                        </div>
                      )}
                    </div>

                    {/* Tier Badge - completely outside and below the card */}
                    <div className="mt-3 flex justify-center">
                      <div
                        className="px-2 py-1 rounded-lg text-xs font-bold text-white"
                        style={{
                          background: 'linear-gradient(135deg, rgba(251, 146, 60, 0.9) 0%, rgba(249, 115, 22, 1) 100%)',
                          boxShadow: '0 4px 15px rgba(251, 146, 60, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
                          border: '1px solid rgba(251, 146, 60, 0.6)'
                        }}
                      >
                        {tierNumber}
                      </div>
                    </div>
                  </div>
                )
            })}
            </div>
          </div>

          {/* Right Arrow - Modern Design */}
          <button
            onClick={goToNextPage}
            disabled={currentPage >= totalPages - 1}
            className={`relative z-10 p-3 rounded-2xl transition-all duration-300 transform ${
              currentPage >= totalPages - 1
                ? 'text-gray-500 cursor-not-allowed opacity-50'
                : 'text-orange-300 hover:text-white hover:scale-110 hover:-translate-y-1'
            }`}
            style={{
              background: currentPage >= totalPages - 1
                ? 'rgba(75, 85, 99, 0.3)'
                : 'linear-gradient(135deg, rgba(251, 146, 60, 0.2) 0%, rgba(249, 115, 22, 0.3) 100%)',
              backdropFilter: 'blur(10px)',
              border: currentPage >= totalPages - 1
                ? '1px solid rgba(75, 85, 99, 0.3)'
                : '1px solid rgba(251, 146, 60, 0.4)',
              boxShadow: currentPage >= totalPages - 1
                ? 'none'
                : '0 4px 20px rgba(251, 146, 60, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>
      </div>


    </div>
  )
}