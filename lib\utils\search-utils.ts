/**
 * Search utility functions for normalizing and matching search terms
 * Handles special characters, apostrophes, colons, and other formatting issues
 */

/**
 * Normalize a string for search matching by removing special characters
 * and converting to lowercase for consistent comparison
 * 
 * @param text - The text to normalize
 * @returns Normalized text for search matching
 */
export function normalizeSearchTerm(text: string): string {
  if (!text || typeof text !== 'string') return ''

  return text
    .toLowerCase()
    .trim()
    // Remove apostrophes (Kha'Zix -> kha<PERSON><PERSON>, Kai'Sa -> kaisa)
    .replace(/'/g, '')
    // Remove colons and replace with space (Project: Ekko -> project ekko)
    .replace(/:/g, ' ')
    // Remove periods (Dr. Mundo -> dr mundo)
    .replace(/\./g, ' ')
    // Remove ampersands and replace with space (Nunu & Willump -> nunu willump)
    .replace(/&/g, ' ')
    // Remove hyphens and replace with space (Kog'Maw -> kog maw)
    .replace(/-/g, ' ')
    // Normalize multiple spaces to single space
    .replace(/\s+/g, ' ')
    .trim()
}

/**
 * Check if a search query matches a target string using normalized comparison
 * Supports partial matching and handles special characters
 * 
 * @param searchQuery - The user's search input
 * @param targetText - The text to search within
 * @returns True if the search query matches the target text
 */
export function matchesSearch(searchQuery: string, targetText: string): boolean {
  if (!searchQuery || !targetText || typeof searchQuery !== 'string' || typeof targetText !== 'string') return false
  
  const normalizedQuery = normalizeSearchTerm(searchQuery)
  const normalizedTarget = normalizeSearchTerm(targetText)
  
  // Split query into words for better matching
  const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0)
  
  // Check if all query words are found in the target
  return queryWords.every(word => normalizedTarget.includes(word))
}

/**
 * Enhanced search matching that also checks for exact matches and word boundaries
 * Provides better ranking for search results
 * 
 * @param searchQuery - The user's search input
 * @param targetText - The text to search within
 * @returns Search match score (higher is better, 0 means no match)
 */
export function getSearchMatchScore(searchQuery: string, targetText: string): number {
  if (!searchQuery || !targetText) return 0
  
  const normalizedQuery = normalizeSearchTerm(searchQuery)
  const normalizedTarget = normalizeSearchTerm(targetText)
  
  // Exact match gets highest score
  if (normalizedQuery === normalizedTarget) return 100
  
  // Check if target starts with query (high priority)
  if (normalizedTarget.startsWith(normalizedQuery)) return 90
  
  // Check if all words match
  const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0)
  const allWordsMatch = queryWords.every(word => normalizedTarget.includes(word))
  
  if (!allWordsMatch) return 0
  
  // Calculate score based on how many words match at word boundaries
  let score = 50
  queryWords.forEach(word => {
    // Word boundary match gets bonus points
    const wordBoundaryRegex = new RegExp(`\\b${word}`, 'i')
    if (wordBoundaryRegex.test(normalizedTarget)) {
      score += 10
    }
  })
  
  return score
}

/**
 * Filter and sort an array of items based on search query
 * 
 * @param items - Array of items to search
 * @param searchQuery - The search query
 * @param getSearchableText - Function to extract searchable text from each item
 * @returns Filtered and sorted array of items
 */
export function searchAndSort<T>(
  items: T[],
  searchQuery: string,
  getSearchableText: (item: T) => string | string[]
): T[] {
  if (!searchQuery.trim()) return items
  
  const itemsWithScores = items
    .map(item => {
      const searchableTexts = Array.isArray(getSearchableText(item)) 
        ? getSearchableText(item) as string[]
        : [getSearchableText(item) as string]
      
      // Get the highest score from all searchable texts
      const maxScore = Math.max(
        ...searchableTexts.map(text => getSearchMatchScore(searchQuery, text))
      )
      
      return { item, score: maxScore }
    })
    .filter(({ score }) => score > 0)
    .sort((a, b) => b.score - a.score)
  
  return itemsWithScores.map(({ item }) => item)
}

/**
 * Simple search function that uses normalized matching
 * For cases where you just need a boolean match result
 *
 * @param items - Array of items to search
 * @param searchQuery - The search query
 * @param getSearchableText - Function to extract searchable text from each item
 * @returns Filtered array of items
 */
export function simpleSearch<T>(
  items: T[],
  searchQuery: string,
  getSearchableText: (item: T) => string | string[]
): T[] {
  if (!searchQuery.trim()) return items

  return items.filter(item => {
    const searchableTexts = Array.isArray(getSearchableText(item))
      ? getSearchableText(item) as string[]
      : [getSearchableText(item) as string]

    return searchableTexts.some(text => matchesSearch(searchQuery, text))
  })
}

/**
 * Calculate Levenshtein distance between two strings
 * Used for fuzzy matching in "Did You Mean?" suggestions
 *
 * @param str1 - First string
 * @param str2 - Second string
 * @returns The edit distance between the two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      )
    }
  }

  return matrix[str2.length][str1.length]
}

/**
 * Calculate similarity score between two strings (0-1, where 1 is identical)
 *
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Similarity score between 0 and 1
 */
function calculateSimilarity(str1: string, str2: string): number {
  const maxLength = Math.max(str1.length, str2.length)
  if (maxLength === 0) return 1

  const distance = levenshteinDistance(str1, str2)
  return (maxLength - distance) / maxLength
}

/**
 * Find "Did You Mean?" suggestions for a search query
 *
 * @param searchQuery - The user's search input
 * @param availableTerms - Array of available search terms to suggest from
 * @param maxSuggestions - Maximum number of suggestions to return (default: 3)
 * @param minSimilarity - Minimum similarity threshold (default: 0.6)
 * @returns Array of suggested terms sorted by similarity
 */
export function getDidYouMeanSuggestions(
  searchQuery: string,
  availableTerms: string[],
  maxSuggestions: number = 3,
  minSimilarity: number = 0.5
): string[] {
  if (!searchQuery.trim() || availableTerms.length === 0) return []

  const normalizedQuery = normalizeSearchTerm(searchQuery)

  // Calculate similarity scores for all terms
  const suggestions = availableTerms
    .map(term => {
      const normalizedTerm = normalizeSearchTerm(term)
      const similarity = calculateSimilarity(normalizedQuery, normalizedTerm)

      return {
        term,
        similarity,
        normalizedTerm
      }
    })
    .filter(({ similarity, normalizedTerm }) => {
      // Filter out exact matches and terms below similarity threshold
      return similarity >= minSimilarity && normalizedTerm !== normalizedQuery
    })
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, maxSuggestions)
    .map(({ term }) => term)

  return suggestions
}

/**
 * Enhanced search with "Did You Mean?" suggestions
 * Returns both search results and suggestions when no results are found
 *
 * @param items - Array of items to search
 * @param searchQuery - The search query
 * @param getSearchableText - Function to extract searchable text from each item
 * @param availableTerms - Array of available search terms for suggestions
 * @returns Object containing results and suggestions
 */
export function searchWithSuggestions<T>(
  items: T[],
  searchQuery: string,
  getSearchableText: (item: T) => string | string[],
  availableTerms: string[]
): {
  results: T[]
  suggestions: string[]
  hasResults: boolean
} {
  const results = simpleSearch(items, searchQuery, getSearchableText)
  const hasResults = results.length > 0

  // Only provide suggestions if no results were found
  const suggestions = hasResults
    ? []
    : getDidYouMeanSuggestions(searchQuery, availableTerms, 3, 0.4)

  return {
    results,
    suggestions,
    hasResults
  }
}
